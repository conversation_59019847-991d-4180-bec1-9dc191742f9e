﻿namespace SignalRChat.Core.DI
{
    public static class InjectSignalRChatCoreLayerServices
    {
        public static IServiceCollection InjectSignalRChatCoreServices(this IServiceCollection services)
            => services.InjectSignalRChatCoreServices<SignalRConnectionManager>();

        public static IServiceCollection InjectSignalRChatCoreServices<TSignalRConnectionManager>(this IServiceCollection services)
            where TSignalRConnectionManager : class, ISignalRConnectionManager
        {
            services.AddHttpContextAccessor();

            services.AddSingleton<ISession, Session>();
            services.AddSingleton<ISignalRConnectionManager, TSignalRConnectionManager>();
            services.AddScoped<ILoginService, LoginService>();
            services.AddScoped<ILogoutService, LogoutService>();
            services.AddScoped<ICreateGroupService, CreateGroupService>();
            services.AddScoped<IJoinGroupService, JoinGroupService>();
            services.AddScoped<ILeaveGroupService, LeaveGroupService>();

            services.AddScoped<IReceiveChatMessageManager, ReceiveChatMessageManager>();
            services.AddScoped<ISendChatMessageManager, SendChatMessageManager>();
            services.AddScoped<IParticipantTypingService, ParticipantTypingService>();
            services.AddScoped<ITextMessageService, TextMessageService>();
            services.AddScoped<IImageMessageService, ImageMessageService>();
            services.AddScoped<IRecordMessageService, RecordMessageService>();
            services.AddScoped<IStreamMessageService, StreamMessageService>();
            services.AddScoped<IBuzzMessageService, BuzzMessageService>();

            return services;
        }

        public static IServiceCollection UseSignalRChat(this IServiceCollection services, Action<DbContextOptionsBuilder>? dbContextOptionsAction = null)
        {
            services.UseSignalRChat(Logger.CreateSerilog(), dbContextOptionsAction);

            return services;
        }

        public static IServiceCollection UseSignalRChat(this IServiceCollection services, ILogger logger, Action<DbContextOptionsBuilder>? dbContextOptionsAction = null)
        {
            services.AddSerilog(logger);

            if (dbContextOptionsAction != null)
            {
                services.AddDbContext<ApplicationDbContext>(options =>
                {
                    dbContextOptionsAction(options);
                    options.UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking);
                });
            }
            else
            {
                services.AddDbContext<ApplicationDbContext>(options =>
                {
                    options.UseInMemoryDatabase("SignalRChatDb");
                    options.UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking);
                });
            }

            services.AddScoped<IRepository, EFCoreRepository>();

            services.AddSignalR(options =>
            {
                options.EnableDetailedErrors = true;
                options.MaximumReceiveMessageSize = null;
            }).AddJsonProtocol(options =>
            {
                options.PayloadSerializerOptions.PropertyNamingPolicy = null;
                options.PayloadSerializerOptions.IgnoreReadOnlyFields = true;
                options.PayloadSerializerOptions.IgnoreReadOnlyProperties = true;
                options.PayloadSerializerOptions.ReferenceHandler = ReferenceHandler.IgnoreCycles;
            });

            return services;
        }
    }
}
