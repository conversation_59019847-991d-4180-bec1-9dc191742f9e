﻿global using Microsoft.AspNetCore.Builder;
global using Microsoft.AspNetCore.SignalR;
global using Microsoft.AspNetCore.SignalR.Client;
global using Microsoft.EntityFrameworkCore;
global using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
global using Microsoft.Extensions.DependencyInjection;
global using NAudio;
global using NAudio.Wave;
global using Serilog;
global using Serilog.Events;
global using SignalRChat.Core.Data;
global using SignalRChat.Core.Data.Repositories;
global using SignalRChat.Core.Enums;
global using SignalRChat.Core.Helpers;
global using SignalRChat.Core.Infrastructure;
global using SignalRChat.Core.Infrastructure.ConnectionManager;
global using SignalRChat.Core.Infrastructure.ReceiveManager;
global using SignalRChat.Core.Infrastructure.SendManager;
global using SignalRChat.Core.Models;
global using SignalRChat.Core.Modules.ChatMessage;
global using SignalRChat.Core.Modules.ChatMessage.BuzzMessage.Events;
global using SignalRChat.Core.Modules.ChatMessage.BuzzMessage.Services;
global using SignalRChat.Core.Modules.ChatMessage.BuzzMessage.ViewModel;
global using SignalRChat.Core.Modules.ChatMessage.ImageMessage.Events;
global using SignalRChat.Core.Modules.ChatMessage.ImageMessage.Services;
global using SignalRChat.Core.Modules.ChatMessage.ImageMessage.ViewModel;
global using SignalRChat.Core.Modules.ChatMessage.ParticipantTyping.Events;
global using SignalRChat.Core.Modules.ChatMessage.ParticipantTyping.Services;
global using SignalRChat.Core.Modules.ChatMessage.ParticipantTyping.ViewModel;
global using SignalRChat.Core.Modules.ChatMessage.RecordMessage.Events;
global using SignalRChat.Core.Modules.ChatMessage.RecordMessage.Services;
global using SignalRChat.Core.Modules.ChatMessage.RecordMessage.ViewModel;
global using SignalRChat.Core.Modules.ChatMessage.StreamMessage.Events;
global using SignalRChat.Core.Modules.ChatMessage.StreamMessage.Services;
global using SignalRChat.Core.Modules.ChatMessage.StreamMessage.ViewModel;
global using SignalRChat.Core.Modules.ChatMessage.TextMessage.Events;
global using SignalRChat.Core.Modules.ChatMessage.TextMessage.Services;
global using SignalRChat.Core.Modules.ChatMessage.TextMessage.ViewModel;
global using SignalRChat.Core.Modules.CreateGroup.Events;
global using SignalRChat.Core.Modules.CreateGroup.Services;
global using SignalRChat.Core.Modules.CreateGroup.ViewModel;
global using SignalRChat.Core.Modules.JoinGroup.Events;
global using SignalRChat.Core.Modules.JoinGroup.Services;
global using SignalRChat.Core.Modules.JoinGroup.ViewModel;
global using SignalRChat.Core.Modules.LeaveGroup.Events;
global using SignalRChat.Core.Modules.LeaveGroup.Services;
global using SignalRChat.Core.Modules.LeaveGroup.ViewModel;
global using SignalRChat.Core.Modules.Login.Events;
global using SignalRChat.Core.Modules.Login.Services;
global using SignalRChat.Core.Modules.Login.ViewModel;
global using SignalRChat.Core.Modules.Logout.Events;
global using SignalRChat.Core.Modules.Logout.Services;
global using SignalRChat.Core.Modules.Logout.ViewModel;
global using SignalRChat.Core.ViewModels;
global using System.ComponentModel;
global using System.Diagnostics;
global using System.Diagnostics.CodeAnalysis;
global using System.Drawing;
global using System.Drawing.Drawing2D;
global using System.Drawing.Imaging;
global using System.Globalization;
global using System.Net;
global using System.Runtime.CompilerServices;
global using System.Runtime.InteropServices;
global using System.Text;
global using System.Text.Json.Serialization;
