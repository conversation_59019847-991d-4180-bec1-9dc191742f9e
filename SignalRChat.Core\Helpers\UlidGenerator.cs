﻿namespace SignalRChat.Core.Helpers
{
    public static class UlidGenerator
    {
        public static Ulid Generator(string? inputString = null)
        {
            if (Debugger.IsAttached)
            {
                DateTimeOffset dateTimeOffset = DateTimeOffset.MinValue;
                inputString ??= new Random().Next(1, 100000).ToString();
                byte[] randomnessBytes = GetFixedRandomness(inputString, 10);
                Ulid newUlid = Ulid.NewUlid(dateTimeOffset, randomnessBytes);
                return newUlid;
            }
            return Ulid.NewUlid();
        }

        public static bool IsValid(this Ulid ulid)
        {
            if (ulid == Ulid.Empty)
                return false;
            return true;
        }

        public static bool IsValid([NotNullWhen(true)] this Ulid? ulid)
        {
            if (ulid == null || !ulid.HasValue || ulid == Ulid.Empty)
                return false;
            return true;
        }

        static byte[] GetFixedRandomness(string input, int length)
        {
            byte[] bytes = Encoding.UTF8.GetBytes(input);

            if (bytes.Length > length)
                return bytes[..length]; // Trim if too long
            else if (bytes.Length < length)
            {
                byte[] padded = new byte[length];
                Array.Copy(bytes, padded, bytes.Length);
                return padded; // Pad if too short
            }

            return bytes;
        }
    }
}
