﻿namespace SignalRChat.Core.Modules.ChatMessage.ImageMessage.Services
{
    public class ImageMessageService : BaseChatMessage, IImageMessageService
    {
        public event ImageMessageReceivedEventHandler OnNewImageMessage;

        public ImageMessageService(
            ISignalRConnectionManager signalRConnectionManager,
            ISendChatMessageManager sendChatMessageManager,
            IReceiveChatMessageManager receiveChatMessageManager,
            ISession session) : base(signalRConnectionManager, sendChatMessageManager, receiveChatMessageManager, session)
        {
            _signalRConnectionManager.OnImageMessageReceived += Receive;
        }

        public async Task<bool> SendAsync(byte[] image)
        {
            ImageMessageRequest request = new ImageMessageRequest(
                UlidGenerator.Generator(),
                _session.User.ClientId,
                _session.SelectedChatCard.ClientId,
                image
            );

            if (CanSend(request))
                return await SendAsync(request);

            return false;
        }

        public bool CanSend(ImageMessageRequest? request)
        {
            return base.CanSend(request);
        }

        public async Task<bool> SendAsync(ImageMessageRequest request)
        {
            bool result = await _sendChatMessageManager.SendAsync(request);
            if (result)
            {
                ImageChatMessageDto imageChatMessageDto = new ImageChatMessageDto(request);
                _session.SelectedChatCard.ChatMessages.Add(imageChatMessageDto);
            }
            return result;
        }

        public async void Receive(ImageMessageResponse? response)
        {
            if (response is null)
                return;

            await _receiveChatMessageManager.ReceiveAsync(response);
            OnNewImageMessage?.Invoke(response);
            //var imagesDirectory = Path.Combine(Environment.CurrentDirectory, "Image Messages");
            //if (!Directory.Exists(imagesDirectory)) Directory.CreateDirectory(imagesDirectory);

            //var imagesCount = Directory.EnumerateFiles(imagesDirectory).Count() + 1;
            //var imagePath = Path.Combine(imagesDirectory, $"IMG_{imagesCount}.jpg");

            //ImageConverter converter = new ImageConverter();
            //using (Image? image = (Image?)converter.ConvertFrom(response.Image))
            //{
            //    if (image != null)
            //        image.Save(imagePath);
            //}
        }
    }
}
