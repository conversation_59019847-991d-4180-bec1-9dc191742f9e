﻿namespace SignalRChat.Core.Models
{
    public class ChatMessage : ViewModelBase
    {
        // Parameterless constructor for EF Core
        internal ChatMessage() { }

        public ChatMessage(Ulid messageId, Ulid senderId, Ulid receiverId, DateTime dateTime, MessageType messageType)
        {
            MessageId = messageId;
            SenderId = senderId;
            ReceiverId = receiverId;
            DateTime = dateTime;
            MessageType = messageType;
        }

        public ChatMessage(ChatMessageDto chatMessageDto)
        {
            MessageId = chatMessageDto.MessageId;
            SenderId = chatMessageDto.SenderId;
            ReceiverId = chatMessageDto.ReceiverId;
            DateTime = chatMessageDto.DateTime;
            MessageType = chatMessageDto.MessageType;
        }

        public ChatMessage(IChatMessageResponse response, MessageType messageType)
        {
            MessageId = response.MessageId;
            SenderId = response.SenderId;
            ReceiverId = response.ReceiverId;
            DateTime = DateTime.Now;
            MessageType = messageType;
        }

        public Ulid MessageId { get; private set; }
        public Ulid SenderId { get; private set; }
        public Client Sender { get; private set; }
        public Ulid ReceiverId { get; private set; }
        public Client Receiver { get; private set; }
        public DateTime DateTime { get; private set; }
        public MessageType MessageType { get; private set; }
    }

    public class TextChatMessage : ChatMessage
    {
        // Parameterless constructor for EF Core
        private TextChatMessage() { }

        public TextChatMessage(Ulid messageId, Ulid senderId, Ulid receiverId, string message) : base(messageId, senderId, receiverId, DateTime.Now, MessageType.Text)
        {
            Message = message;
        }

        public TextChatMessage(TextChatMessageDto textChatMessageDto) : base(textChatMessageDto)
        {
            Message = textChatMessageDto.Message;
        }

        public TextChatMessage(TextMessageResponse response) : base(response, MessageType.Text)
        {
            Message = response.Message;
        }

        public string Message { get; private set; }
    }

    public class ImageChatMessage : ChatMessage
    {
        // Parameterless constructor for EF Core
        private ImageChatMessage() { }

        public ImageChatMessage(Ulid messageId, Ulid senderId, Ulid receiverId, byte[] image) : base(messageId, senderId, receiverId, DateTime.Now, MessageType.Image)
        {
            Image = image;
        }

        public ImageChatMessage(ImageChatMessageDto imageChatMessageDto) : base(imageChatMessageDto)
        {
            Image = imageChatMessageDto.Image;
        }

        public ImageChatMessage(ImageMessageResponse response) : base(response, MessageType.Image)
        {
            Image = response.Image;
        }

        public byte[] Image { get; private set; }
    }

    public class RecordChatMessage : ChatMessage
    {
        // Parameterless constructor for EF Core
        private RecordChatMessage() { }

        public RecordChatMessage(Ulid messageId, Ulid senderId, Ulid receiverId, byte[] record) : base(messageId, senderId, receiverId, DateTime.Now, MessageType.Record)
        {
            Record = record;
        }

        public RecordChatMessage(RecordChatMessageDto recordChatMessageDto) : base(recordChatMessageDto)
        {
            Record = recordChatMessageDto.Record;
        }

        public RecordChatMessage(RecordMessageResponse response) : base(response, MessageType.Record)
        {
            Record = response.Record;
        }

        public byte[] Record { get; private set; }
    }

    public class StreamChatMessage : ChatMessage
    {
        // Parameterless constructor for EF Core
        private StreamChatMessage() { }

        public StreamChatMessage(Ulid messageId, Ulid senderId, Ulid receiverId, Ulid streamId) : base(messageId, senderId, receiverId, DateTime.Now, MessageType.Stream)
        {
            StreamId = streamId;
        }

        public StreamChatMessage(StreamChatMessageDto streamChatMessageDto) : base(streamChatMessageDto)
        {
            StreamId = streamChatMessageDto.StreamId;
        }

        public StreamChatMessage(StreamMessageResponse response) : base(response, MessageType.Stream)
        {
            StreamId = response.StreamId;
        }

        public Ulid StreamId { get; private set; }
    }
}
