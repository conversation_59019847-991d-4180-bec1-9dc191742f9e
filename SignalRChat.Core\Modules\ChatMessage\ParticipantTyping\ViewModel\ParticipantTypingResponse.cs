﻿namespace SignalRChat.Core.Modules.ChatMessage.ParticipantTyping.ViewModel
{
    public record ParticipantTypingResponse : BaseChatMessageResponse
    {
        [JsonConstructor]
        public ParticipantTypingResponse(Ulid messageId, Ulid senderId, Ulid receiverId) : base(messageId, senderId, receiverId, false)
        {
        }

        public ParticipantTypingResponse(ParticipantTypingRequest request) : this(request.MessageId, request.SenderId, request.ReceiverId)
        {
        }
    }
}
