﻿namespace SignalRChat.Core.Helpers
{
    public class VoiceRecorder
    {
        private WaveInEvent waveIn;
        private WaveFileWriter writer;

        public VoiceRecorder()
        {
        }

        public void StartRecord(string FilePath)
        {
            try
            {
                if (!Path.HasExtension(FilePath))
                    FilePath = FilePath + ".wav";
                Initalizing();
                writer = new WaveFileWriter(FilePath, waveIn.WaveFormat);
                waveIn.StartRecording();
            }
            catch (MmException ex)
            {
                if (ex.Message == "BadDeviceId calling waveInOpen")
                    throw new Exception("There is no mic connected");
                throw;
            }
            catch (Exception)
            {
                throw;
            }
        }

        public string StopRecord()
        {
            string beforeStop = writer.Filename;
            waveIn.StopRecording();
            Thread.Sleep(1000); // Give some time for the last data to be processed
            writer?.Dispose();
            writer = null!;
            string recordPath = writer?.Filename ?? beforeStop;
            return recordPath;
        }

        private void Initalizing()
        {
            waveIn = new WaveInEvent();
            waveIn.WaveFormat = new WaveFormat();

            waveIn.DataAvailable += (s, e) =>
            {
                writer?.Write(e.Buffer, 0, e.BytesRecorded);
                if (writer?.Position > waveIn.WaveFormat.AverageBytesPerSecond * 30)
                {
                    waveIn.StopRecording();
                }
            };
            waveIn.RecordingStopped += (s, e) =>
            {
                waveIn?.Dispose();
                waveIn = null!;
            };

        }
    }
}
