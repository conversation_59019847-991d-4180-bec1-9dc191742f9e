﻿namespace SignalRChat.Core.Modules.ChatMessage.TextMessage.ViewModel
{
    public record TextMessageRequest : BaseChatMessageRequest
    {
        [JsonConstructor]
        public TextMessageRequest(Ulid messageId, Ulid senderId, Ulid receiverId, string message) : base(messageId, senderId, receiverId)
        {
            Message = message;
        }

        [JsonInclude]
        public string Message { get; private set; }

        public override bool IsValid()
        {
            return
                base.IsValid()
                && !string.IsNullOrEmpty(Message);
        }
    }
}
