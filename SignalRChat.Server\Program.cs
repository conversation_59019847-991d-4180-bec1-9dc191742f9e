﻿using Microsoft.Extensions.Configuration;
using SignalRChat.Core.Configuration;
using SignalRChat.Core.Events;

namespace SignalRChat.Server
{
    internal class Program
    {
        static void Main(string[] args)
        {
            CreateHostBuilder(args).Build().Run();
        }

        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
            .ConfigureWebHostDefaults(webBuilder =>
            {
                webBuilder.UseUrls("http://localhost:8080/");
                webBuilder.UseStartup<Startup>();
            });
    }

    public class Startup
    {
        private readonly IConfiguration _configuration;

        public Startup(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public void ConfigureServices(IServiceCollection services)
        {
            // Option 1: Use the new comprehensive configuration system
            services.UseSignalRChatWithConfiguration(_configuration, builder =>
            {
                builder
                    .ConfigureDatabase(db =>
                    {
                        // Override database settings if needed
                        db.Provider = DatabaseProvider.MySQL;
                        db.EnableMigrations = true;
                    })
                    .ConfigureHub(hub =>
                    {
                        // Override hub settings if needed
                        hub.EnableDetailedErrors = true;
                        hub.HubPath = "/signalchat";
                    })
                    .ConfigureLogging(logging =>
                    {
                        // Override logging settings if needed
                        logging.MinimumLevel = LogLevel.Information;
                        logging.EnableConsoleLogging = true;
                    })
                    .ConfigureCors(cors =>
                    {
                        // Override CORS settings if needed
                        cors.AllowedOrigins.Add("http://localhost:3000");
                    });
            });

            // Option 2: Traditional way (still supported for backward compatibility)
            /*
            services.AddCors(options =>
            {
                options.AddDefaultPolicy(builder =>
                {
                    string defaultOrigins = "http://localhost:8080";
                    builder.WithOrigins(defaultOrigins)
                           .AllowAnyHeader()
                           .AllowAnyMethod()
                           .AllowCredentials();
                });
            });

            string connectionString = "Server=localhost;Database=SignalRChat_db;Uid=root;Pwd=***;default command timeout=60;";
            services.UseSignalRChat(options =>
            {
                options.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString));
            });
            */

            // Add event bus for integration
            services.AddSingleton<IEventBus, InMemoryEventBus>();
        }

        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }

            app.UseRouting();

            app.UseCors();

            app.UseSignalRChat<SignalRChatHub>();
        }
    }
}
