﻿namespace SignalRChat.Core.Modules.ChatMessage.ImageMessage.ViewModel
{
    public record ImageMessageRequest : BaseChatMessageRequest
    {
        [JsonConstructor]
        public ImageMessageRequest(Ulid messageId, Ulid senderId, Ulid receiverId, byte[] image) : base(messageId, senderId, receiverId)
        {
            Image = image;
        }

        [JsonInclude]
        public byte[] Image { get; private set; }

        public override bool IsValid()
        {
            return
                base.IsValid()
                && Image != null
                && Image.Length != 0;
        }
    }
}
