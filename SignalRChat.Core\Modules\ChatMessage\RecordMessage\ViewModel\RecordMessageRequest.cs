﻿namespace SignalRChat.Core.Modules.ChatMessage.RecordMessage.ViewModel
{
    public record RecordMessageRequest : BaseChatMessageRequest
    {
        [JsonConstructor]
        public RecordMessageRequest(Ulid messageId, Ulid senderId, Ulid receiverId, byte[] record) : base(messageId, senderId, receiverId)
        {
            Record = record;
        }

        [JsonInclude]
        public byte[] Record { get; private set; }

        public bool IsValid()
        {
            return
                base.IsValid()
                && Record != null
                && Record.Length > 0;
        }
    }
}
