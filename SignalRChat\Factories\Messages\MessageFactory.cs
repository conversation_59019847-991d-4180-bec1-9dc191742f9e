﻿namespace SignalRChat.Factories.Messages
{
    public class MessageFactory
    {
        public static IMessageDisplayStrategy CreateMessage(ISession session, MessageType messageDisplayType)
        {
            switch (messageDisplayType)
            {
                case MessageType.Text:
                    return new TextMessageDisplayStrategy(new TextMessage());
                case MessageType.Image:
                    return new ImageMessageDisplayStrategy(new ImageMessage(session));
                case MessageType.Record:
                    return new RecordMessageDisplayStrategy(new RecordMessage());
                case MessageType.Stream:
                    return new StreamMessageDisplayStrategy(new StreamMessage(session));
                default:
                    throw new NotImplementedException($"Need to implement the {nameof(IMessageDisplayStrategy)} interface");
            }
        }
    }
}
