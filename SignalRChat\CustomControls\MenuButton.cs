﻿namespace SignalRChat.CustomControls
{
    public class MenuButton : Button, IDisposable
    {
        private readonly SynchronizationContext? _syncContext;

        public MenuButton()
        {
            _syncContext = SynchronizationContext.Current;
        }

        [DefaultValue(null)]
        public ContextMenuStrip? Menu { get; set; }

        [DefaultValue(false)]
        public bool ShowMenuUnderCursor { get; set; }

        public override string? Text
        {
            get => base.Text;
            set
            {
                if (_syncContext != null)
                    _syncContext.Send(_ => base.Text = value, null);
                else
                    base.Text = value;
            }
        }

        protected override void OnTextChanged(EventArgs e)
        {
            if (_syncContext != null)
                _syncContext.Send(_ => base.OnTextChanged(e), null);
            else
                base.OnTextChanged(e);
        }

        protected override void OnMouseDown(MouseEventArgs mevent)
        {
            base.OnMouseDown(mevent);

            if (Menu != null && mevent.Button == MouseButtons.Left)
            {
                Point menuLocation = ShowMenuUnderCursor ? mevent.Location : new Point(0, Height - 1);
                Menu.Show(this, menuLocation);
            }
        }

        protected override void OnPaint(PaintEventArgs pevent)
        {
            base.OnPaint(pevent);

            if (Menu != null)
            {
                int arrowX = ClientRectangle.Width - Padding.Right - 14;
                int arrowY = (ClientRectangle.Height / 2) - 1;

                Color color = Enabled ? ForeColor : SystemColors.ControlDark;
                using (Brush brush = new SolidBrush(color))
                {
                    Point[] arrows = { new Point(arrowX, arrowY), new Point(arrowX + 7, arrowY), new Point(arrowX + 3, arrowY + 4) };
                    pevent.Graphics.FillPolygon(brush, arrows);
                }
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                // Dispose managed resources if any
            }

            base.Dispose(disposing);
        }
    }
}
