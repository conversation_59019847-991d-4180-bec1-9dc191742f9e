﻿namespace SignalRChat.Core.Hubs
{
    public interface ISignalRChatHub
    {
        Task ParticipantDisconnection(Ulid clientId);
        Task ParticipantReconnection(Ulid clientId);
        Task ParticipantLogin(UserDto client);
        Task ParticipantLogout(Ulid clientId);
        Task Login(LoginResponse response);
        Task Logout(LogoutResponse response);
        Task TextMessage(TextMessageResponse response);
        Task ImageMessage(ImageMessageResponse response);
        Task RecordMessage(RecordMessageResponse response);
        Task StreamMessage(StreamMessageResponse response);
        Task BuzzMessage(BuzzMessageResponse response);
        Task ParticipantTyping(ParticipantTypingResponse response);
        Task CreateGroup(CreateGroupResponse response);
        Task JoinGroup(JoinGroupResponse response);
        Task LeaveGroup(LeaveGroupResponse response);
    }
}
