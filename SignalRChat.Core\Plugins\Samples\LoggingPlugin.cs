using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace SignalRChat.Core.Plugins.Samples
{
    /// <summary>
    /// Sample plugin that demonstrates message logging functionality
    /// </summary>
    public class LoggingPlugin : IMessagePlugin, IDisposable
    {
        private ILogger<LoggingPlugin>? _logger;
        private IEventBus? _eventBus;
        private bool _disposed;

        public string Id => "SignalRChat.Plugins.Logging";
        public string Name => "Message Logging Plugin";
        public Version Version => new Version(1, 0, 0);
        public string Description => "Logs all messages for auditing and monitoring purposes";
        public string Author => "SignalR Chat Team";

        public IEnumerable<PluginDependency> Dependencies => new List<PluginDependency>();

        public IEnumerable<Type> SupportedMessageTypes => new[]
        {
            typeof(object) // Support all message types
        };

        public async Task InitializeAsync(IServiceProvider serviceProvider, Microsoft.Extensions.Logging.ILogger logger, CancellationToken cancellationToken = default)
        {
            _logger = serviceProvider.GetService<ILogger<LoggingPlugin>>() ?? 
                     serviceProvider.GetService<ILoggerFactory>()?.CreateLogger<LoggingPlugin>();
            
            _eventBus = serviceProvider.GetService<IEventBus>();

            _logger?.LogInformation("Initializing {PluginName} v{Version}", Name, Version);

            // Subscribe to events if event bus is available
            if (_eventBus != null)
            {
                _eventBus.Subscribe<MessageSentEvent>(OnMessageSent);
                _eventBus.Subscribe<MessageReceivedEvent>(OnMessageReceived);
                _eventBus.Subscribe<UserLoginEvent>(OnUserLogin);
                _eventBus.Subscribe<UserLogoutEvent>(OnUserLogout);
            }

            await Task.CompletedTask;
        }

        public void ConfigureServices(IServiceCollection services)
        {
            // Register any services this plugin needs
            services.AddSingleton<MessageAuditService>();
        }

        public async Task StartAsync(CancellationToken cancellationToken = default)
        {
            _logger?.LogInformation("Starting {PluginName}", Name);
            await Task.CompletedTask;
        }

        public async Task StopAsync(CancellationToken cancellationToken = default)
        {
            _logger?.LogInformation("Stopping {PluginName}", Name);
            await Task.CompletedTask;
        }

        public async Task<MessageProcessingResult> ProcessIncomingMessageAsync(MessageContext context, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger?.LogInformation("Processing incoming message from {SenderId} to {ReceiverId} at {Timestamp}",
                    context.SenderId, context.ReceiverId, context.Timestamp);

                // Log message details (be careful with sensitive data)
                LogMessageDetails(context, "INCOMING");

                // Publish event for other systems
                if (_eventBus != null)
                {
                    var messageEvent = new MessageReceivedEvent
                    {
                        MessageId = context.Properties.TryGetValue("MessageId", out var msgId) ? msgId.ToString() ?? string.Empty : string.Empty,
                        SenderId = context.SenderId,
                        ReceiverId = context.ReceiverId,
                        MessageType = context.Message.GetType().Name,
                        MessageContent = SanitizeMessageContent(context.Message),
                        ReceivedAt = context.Timestamp
                    };

                    await _eventBus.PublishAsync(messageEvent, cancellationToken);
                }

                return new MessageProcessingResult
                {
                    Success = true,
                    ProcessedMessage = context.Message, // Pass through unchanged
                    ShouldContinueProcessing = true
                };
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error processing incoming message in {PluginName}", Name);
                return new MessageProcessingResult
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    ShouldContinueProcessing = true // Don't break the chain
                };
            }
        }

        public async Task<MessageProcessingResult> ProcessOutgoingMessageAsync(MessageContext context, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger?.LogInformation("Processing outgoing message from {SenderId} to {ReceiverId} at {Timestamp}",
                    context.SenderId, context.ReceiverId, context.Timestamp);

                // Log message details
                LogMessageDetails(context, "OUTGOING");

                // Publish event for other systems
                if (_eventBus != null)
                {
                    var messageEvent = new MessageSentEvent
                    {
                        MessageId = context.Properties.TryGetValue("MessageId", out var msgId) ? msgId.ToString() ?? string.Empty : string.Empty,
                        SenderId = context.SenderId,
                        ReceiverId = context.ReceiverId,
                        MessageType = context.Message.GetType().Name,
                        MessageContent = SanitizeMessageContent(context.Message),
                        SentAt = context.Timestamp
                    };

                    await _eventBus.PublishAsync(messageEvent, cancellationToken);
                }

                return new MessageProcessingResult
                {
                    Success = true,
                    ProcessedMessage = context.Message, // Pass through unchanged
                    ShouldContinueProcessing = true
                };
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error processing outgoing message in {PluginName}", Name);
                return new MessageProcessingResult
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    ShouldContinueProcessing = true // Don't break the chain
                };
            }
        }

        private void LogMessageDetails(MessageContext context, string direction)
        {
            var messageType = context.Message.GetType().Name;
            var messageSize = EstimateMessageSize(context.Message);

            _logger?.LogDebug("{Direction} Message - Type: {MessageType}, Size: {Size} bytes, From: {SenderId}, To: {ReceiverId}",
                direction, messageType, messageSize, context.SenderId, context.ReceiverId);

            // Log additional properties
            foreach (var property in context.Properties)
            {
                _logger?.LogDebug("Message Property - {Key}: {Value}", property.Key, property.Value);
            }
        }

        private object? SanitizeMessageContent(object message)
        {
            // For security and privacy, don't log the actual message content
            // Instead, return metadata about the message
            return new
            {
                Type = message.GetType().Name,
                Size = EstimateMessageSize(message),
                Timestamp = DateTime.UtcNow
            };
        }

        private int EstimateMessageSize(object message)
        {
            try
            {
                // Simple estimation - in a real plugin you might use more sophisticated methods
                var json = System.Text.Json.JsonSerializer.Serialize(message);
                return System.Text.Encoding.UTF8.GetByteCount(json);
            }
            catch
            {
                return 0;
            }
        }

        private async Task OnMessageSent(MessageSentEvent eventData, CancellationToken cancellationToken)
        {
            _logger?.LogInformation("Event received: Message {MessageId} sent from {SenderId} to {ReceiverId}",
                eventData.MessageId, eventData.SenderId, eventData.ReceiverId);
            await Task.CompletedTask;
        }

        private async Task OnMessageReceived(MessageReceivedEvent eventData, CancellationToken cancellationToken)
        {
            _logger?.LogInformation("Event received: Message {MessageId} received by {ReceiverId} from {SenderId}",
                eventData.MessageId, eventData.ReceiverId, eventData.SenderId);
            await Task.CompletedTask;
        }

        private async Task OnUserLogin(UserLoginEvent eventData, CancellationToken cancellationToken)
        {
            _logger?.LogInformation("Event received: User {UserId} logged in at {LoginTime}",
                eventData.UserId, eventData.LoginAt);
            await Task.CompletedTask;
        }

        private async Task OnUserLogout(UserLogoutEvent eventData, CancellationToken cancellationToken)
        {
            _logger?.LogInformation("Event received: User {UserId} logged out at {LogoutTime}",
                eventData.UserId, eventData.LogoutAt);
            await Task.CompletedTask;
        }

        public async Task DisposeAsync()
        {
            if (!_disposed)
            {
                _logger?.LogInformation("Disposing {PluginName}", Name);

                // Unsubscribe from events
                if (_eventBus != null)
                {
                    _eventBus.UnsubscribeAll<MessageSentEvent>();
                    _eventBus.UnsubscribeAll<MessageReceivedEvent>();
                    _eventBus.UnsubscribeAll<UserLoginEvent>();
                    _eventBus.UnsubscribeAll<UserLogoutEvent>();
                }

                _disposed = true;
            }
            await Task.CompletedTask;
        }

        public void Dispose()
        {
            DisposeAsync().GetAwaiter().GetResult();
        }
    }

    /// <summary>
    /// Sample service that could be used by the plugin
    /// </summary>
    public class MessageAuditService
    {
        private readonly ILogger<MessageAuditService> _logger;

        public MessageAuditService(ILogger<MessageAuditService> logger)
        {
            _logger = logger;
        }

        public async Task AuditMessageAsync(string messageId, string senderId, string receiverId, string messageType)
        {
            _logger.LogInformation("Auditing message {MessageId} of type {MessageType} from {SenderId} to {ReceiverId}",
                messageId, messageType, senderId, receiverId);

            // In a real implementation, you might save to a database, send to an external service, etc.
            await Task.CompletedTask;
        }
    }
}
