﻿namespace SignalRChat.Core.Modules.ChatMessage.TextMessage.ViewModel
{
    public record TextMessageResponse : BaseChatMessageResponse
    {
        [JsonConstructor]
        public TextMessageResponse(Ulid messageId, Ulid senderId, Ulid receiverId, string message) : base(messageId, senderId, receiverId, true)
        {
            Message = message;
        }

        public TextMessageResponse(TextMessageRequest request) : this(request.MessageId, request.SenderId, request.ReceiverId, request.Message)
        {
        }

        [JsonInclude]
        public string Message { get; private set; }
    }
}