﻿namespace SignalRChat.Core.Modules.ChatMessage.StreamMessage.ViewModel
{
    public record StreamMessageResponse : BaseChatMessageResponse
    {
        [JsonConstructor]
        public StreamMessageResponse(Ulid messageId, Ulid senderId, Ulid receiverId, Ulid streamId, byte[] stream) : base(messageId, senderId, receiverId, false)
        {
            StreamId = streamId;
            Stream = stream;
        }

        public StreamMessageResponse(StreamMessageRequest request) : this(request.MessageId, request.SenderId, request.ReceiverId, request.StreamId, request.Stream)
        {
        }

        [JsonInclude]
        public Ulid StreamId { get; private set; }
        [JsonInclude]
        public byte[] Stream { get; private set; }
    }
}
