﻿namespace SignalRChat.Strategies.Messages
{
    public class RecordMessage : Label, IRecordMessageView, IDisposable
    {
        private bool _isRecorderRunning = false;
        public bool IsRecorderRunning
        {
            get => _isRecorderRunning;
            set
            {
                _isRecorderRunning = value;
                Invalidate();
            }
        }

        private double _progress;
        public double Progress
        {
            get => _progress;
            set
            {
                // Ensure progress is within valid range
                _progress = Math.Max(0, Math.Min(100, value));
                // Redraw the panel when the progress changes
                Invalidate();
            }
        }

        public RecordMessage()
        {
            // Set the size and other properties of the button
            Height = 30;
            Dock = DockStyle.Fill;
            Text = ""; // Clear the text to make room for the custom symbol
            Click += OnPlayClicked;
        }

        public event EventHandler? Play;

        private void OnPlayClicked(object? sender, EventArgs e)
        {
            Play?.Invoke(sender, e);
        }

        #region UI
        protected override void OnPaint(PaintEventArgs e)
        {
            base.OnPaint(e);

            checked
            {
                Graphics g = e.Graphics;

                g.SmoothingMode = SmoothingMode.HighQuality;
                g.PixelOffsetMode = PixelOffsetMode.HighQuality;
                g.TextRenderingHint = TextRenderingHint.ClearTypeGridFit;
                GraphicsPath graphicsPath = Rounding.RoundRect(Rounding.FullRectangle(Size, true), 15, Rounding.RoundingStyle.All);

                // Calculate the center point of the button
                Point center = new Point(Width / 2, Height / 2);

                // Move the symbols to the left by adjusting the x-coordinate
                int symbolOffset = 20;

                Color solidBrushColor;
                Color clickBackgroundColor;
                Color backgroundColor = Color.GhostWhite;
                if (backgroundColor.R * 0.2126 + backgroundColor.G * 0.7152 + backgroundColor.B * 0.0722 < 255 / 2)
                {
                    // Dark Color
                    clickBackgroundColor = Rounding.ChangeColorBrightness(backgroundColor, 0.10f);
                }
                else
                {
                    // Light Color
                    clickBackgroundColor = Rounding.ChangeColorBrightness(backgroundColor, -0.10f);
                }

                if (State != MouseState.Over)
                {
                    if (State != MouseState.Down)
                    {
                        solidBrushColor = backgroundColor;
                    }
                    else
                    {
                        solidBrushColor = clickBackgroundColor;
                    }
                }
                else
                {
                    solidBrushColor = backgroundColor;
                }

                // Define the starting and ending colors for the gradient
                Color startColor = Color.FromArgb(230, 100, 101); // Light color
                Color endColor = Color.FromArgb(145, 152, 229); // Dark color

                // Define the rectangle to fill with the gradient based on progress
                int fillWidth = (int)(Width * Progress);
                Rectangle gradientRect = new Rectangle(0, 0, fillWidth, Height);

                // Create a rounded rectangle path
                GraphicsPath roundedRect;
                if (Progress < 0.035)
                    roundedRect = Rounding.RoundRect(gradientRect, 15, Rounding.RoundingStyle.Left); // Adjust the radius as needed
                else
                    roundedRect = Rounding.RoundRect(gradientRect, 15, Rounding.RoundingStyle.All); // Adjust the radius as needed

                using (SolidBrush solidBrush = new SolidBrush(solidBrushColor))
                {
                    using (Pen pen = new Pen(backgroundColor, 0))
                    {
                        g.FillPath(solidBrush, graphicsPath);
                        g.DrawPath(pen, graphicsPath);

                        if (fillWidth != 0)
                        {
                            // Create a LinearGradientBrush for the gradient effect
                            using (LinearGradientBrush gradientBrush = new LinearGradientBrush(
                                gradientRect,
                                startColor,
                                endColor,
                                LinearGradientMode.Horizontal)) // Change mode as needed
                            {
                                // Fill the rounded rectangle path with the gradient color
                                g.FillPath(gradientBrush, roundedRect);
                            }
                        }

                        if (solidBrushColor != Color.Transparent)
                            Rounding.CenterString(g, Text, Font, ForeColor, Rounding.FullRectangle(Size, false));
                    }
                }

                if (!IsRecorderRunning)
                    // Draw play symbol on the middle left of the button
                    DrawPlaySymbol(g, new Point(symbolOffset, center.Y), 20);
                else
                    // Draw pause symbol on the middle left of the button
                    DrawPauseSymbol(g, new Point(symbolOffset, center.Y), 20);
            }
        }

        private void DrawPlaySymbol(Graphics g, Point center, int size)
        {
            Point[] playPoints = new Point[3];

            playPoints[0] = new Point(center.X - size / 2, center.Y + size / 2);
            playPoints[1] = new Point(center.X + size / 2, center.Y);
            playPoints[2] = new Point(center.X - size / 2, center.Y - size / 2);

            g.FillPolygon(Brushes.Green, playPoints);
        }

        private void DrawPauseSymbol(Graphics g, Point center, int size)
        {
            int pauseBarWidth = size / 5;
            int pauseBarHeight = size / 1;

            Rectangle leftBar = new Rectangle(center.X - size / 3 - pauseBarWidth / 2, center.Y - pauseBarHeight / 2, pauseBarWidth, pauseBarHeight);
            Rectangle rightBar = new Rectangle(center.X + size / 3 - pauseBarWidth / 2, center.Y - pauseBarHeight / 2, pauseBarWidth, pauseBarHeight);

            g.FillRectangle(Brushes.Blue, leftBar);
            g.FillRectangle(Brushes.Blue, rightBar);
        }

        protected override void OnMouseEnter(EventArgs e)
        {
            base.OnMouseEnter(e);
            State = MouseState.Over;
            Invalidate();
        }

        protected override void OnMouseLeave(EventArgs e)
        {
            base.OnMouseLeave(e);
            State = MouseState.None;
            Invalidate();
        }

        protected override void OnMouseUp(MouseEventArgs e)
        {
            base.OnMouseUp(e);
            State = MouseState.Over;
            Invalidate();
        }

        protected override void OnMouseDown(MouseEventArgs e)
        {
            base.OnMouseDown(e);
            State = MouseState.Down;
            Invalidate();
        }

        private MouseState State;
        public enum MouseState : byte
        {
            None,
            Over,
            Down
        }
        #endregion

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                // Unsubscribe from events
                Click -= OnPlayClicked;
            }

            base.Dispose(disposing);
        }
    }
}
