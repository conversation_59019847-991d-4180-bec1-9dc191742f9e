﻿namespace SignalRChat.Core.Modules.ChatMessage.ImageMessage.ViewModel
{
    public record ImageMessageResponse : BaseChatMessageResponse
    {
        [JsonConstructor]
        public ImageMessageResponse(Ulid messageId, Ulid senderId, Ulid receiverId, byte[] image) : base(messageId, senderId, receiverId, true)
        {
            Image = image;
        }

        public ImageMessageResponse(ImageMessageRequest request) : this(request.MessageId, request.SenderId, request.ReceiverId, request.Image)
        {
        }

        [JsonInclude]
        public byte[] Image { get; private set; }
    }
}
