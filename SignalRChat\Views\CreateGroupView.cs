﻿namespace SignalRChat.Views
{
    public partial class CreateGroupView : Form, IDisposable
    {
        public string GroupName => txtGroupName.Text;
        public byte[] GroupPhoto => Converters.ImageToByteArray(pbxProfilePicture.Image);

        public CreateGroupView()
        {
            InitializeComponent();
            btnCreate.Click += BtnCreate_Click;
        }

        private void BtnCreate_Click(object? sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(GroupName))
                return;

            DialogResult = DialogResult.OK;
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                // Unsubscribe from events
                btnCreate.Click -= BtnCreate_Click;

                // Dispose managed resources
                components?.Dispose();
            }

            if (disposing && (components != null))
            {
                components.Dispose();
            }

            base.Dispose(disposing);
        }
    }
}
