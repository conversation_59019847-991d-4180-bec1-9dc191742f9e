﻿namespace SignalRChat.Core.Models
{
    public class ChatCard : ViewModelBase, IComparer<ChatCard>
    {
        public ClientDto Client { get; private set; }

        public ChatCard(ClientDto client)
        {
            Client = client;
            var chatMessages = new List<ChatMessageDto>(client.SentMessages).Concat(client.ReceivedMessages).OrderBy(x => x.DateTime);
            ChatMessages = new BindingList<ChatMessageDto>(chatMessages.ToList());
            if (Client.ChatType is ChatType.GroupChat)
                GroupChatCard = new BindingList<ChatCard>();
        }

        public ChatType ChatType => Client.ChatType;
        public string Name => Client.Name;
        public Ulid ClientId => Client.ClientId;
        public byte[] Photo => Client.Photo;

        public BindingList<ChatMessageDto> ChatMessages { get; private set; }
        public BindingList<ChatCard> GroupChatCard { get; private set; }

        private bool _isLoggedIn = true;
        public bool IsLoggedIn
        {
            get { return _isLoggedIn; }
            set { _isLoggedIn = value; OnPropertyChanged(); }
        }

        private bool _hasSentNewMessage;
        public bool HasSentNewMessage
        {
            get { return _hasSentNewMessage; }
            set { _hasSentNewMessage = value; OnPropertyChanged(); }
        }

        private bool _isTyping;
        public bool IsTyping
        {
            get { return _isTyping; }
            set { _isTyping = value; OnPropertyChanged(); }
        }

        public int Compare(ChatCard? x, ChatCard? y)
        {
            if (x == null || y == null)
                return 0;

            // Compare by Name
            return x.ClientId.CompareTo(y.ClientId);
        }
    }
}
