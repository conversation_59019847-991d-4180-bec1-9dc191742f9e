using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace SignalRChat.Core.Events
{
    /// <summary>
    /// In-memory implementation of the event bus
    /// </summary>
    public class InMemoryEventBus : IEventBus, IDisposable
    {
        private readonly ILogger<InMemoryEventBus> _logger;
        private readonly ConcurrentDictionary<Type, ConcurrentBag<object>> _handlers;
        private readonly SemaphoreSlim _publishLock;
        private bool _disposed;

        public InMemoryEventBus(ILogger<InMemoryEventBus> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _handlers = new ConcurrentDictionary<Type, ConcurrentBag<object>>();
            _publishLock = new SemaphoreSlim(1, 1);
        }

        /// <summary>
        /// Publish an event to all registered handlers
        /// </summary>
        public async Task PublishAsync<T>(T eventData, CancellationToken cancellationToken = default) where T : class, IEvent
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(InMemoryEventBus));

            if (eventData == null)
                throw new ArgumentNullException(nameof(eventData));

            var eventType = typeof(T);
            
            _logger.LogDebug("Publishing event {EventType} with ID {EventId}", eventType.Name, eventData.EventId);

            if (!_handlers.TryGetValue(eventType, out var handlers) || handlers.IsEmpty)
            {
                _logger.LogDebug("No handlers registered for event type {EventType}", eventType.Name);
                return;
            }

            var tasks = new List<Task>();

            foreach (var handler in handlers)
            {
                try
                {
                    switch (handler)
                    {
                        case IEventHandler<T> typedHandler:
                            tasks.Add(ExecuteHandlerAsync(typedHandler, eventData, cancellationToken));
                            break;
                        case Func<T, CancellationToken, Task> funcHandler:
                            tasks.Add(ExecuteFuncHandlerAsync(funcHandler, eventData, cancellationToken));
                            break;
                        default:
                            _logger.LogWarning("Unknown handler type {HandlerType} for event {EventType}", 
                                handler.GetType().Name, eventType.Name);
                            break;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error creating task for handler {HandlerType} for event {EventType}", 
                        handler.GetType().Name, eventType.Name);
                }
            }

            if (tasks.Count > 0)
            {
                try
                {
                    await Task.WhenAll(tasks);
                    _logger.LogDebug("Successfully published event {EventType} to {HandlerCount} handlers", 
                        eventType.Name, tasks.Count);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error publishing event {EventType}", eventType.Name);
                    throw;
                }
            }
        }

        /// <summary>
        /// Subscribe to an event type with a typed handler
        /// </summary>
        public void Subscribe<T>(IEventHandler<T> handler) where T : class, IEvent
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(InMemoryEventBus));

            if (handler == null)
                throw new ArgumentNullException(nameof(handler));

            var eventType = typeof(T);
            var handlers = _handlers.GetOrAdd(eventType, _ => new ConcurrentBag<object>());
            handlers.Add(handler);

            _logger.LogDebug("Subscribed handler {HandlerType} to event {EventType}", 
                handler.GetType().Name, eventType.Name);
        }

        /// <summary>
        /// Subscribe to an event type with a function handler
        /// </summary>
        public void Subscribe<T>(Func<T, CancellationToken, Task> handler) where T : class, IEvent
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(InMemoryEventBus));

            if (handler == null)
                throw new ArgumentNullException(nameof(handler));

            var eventType = typeof(T);
            var handlers = _handlers.GetOrAdd(eventType, _ => new ConcurrentBag<object>());
            handlers.Add(handler);

            _logger.LogDebug("Subscribed function handler to event {EventType}", eventType.Name);
        }

        /// <summary>
        /// Unsubscribe a specific handler from an event type
        /// </summary>
        public void Unsubscribe<T>(IEventHandler<T> handler) where T : class, IEvent
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(InMemoryEventBus));

            if (handler == null)
                throw new ArgumentNullException(nameof(handler));

            var eventType = typeof(T);
            
            if (_handlers.TryGetValue(eventType, out var handlers))
            {
                // Since ConcurrentBag doesn't support removal, we need to recreate it
                var newHandlers = new ConcurrentBag<object>();
                foreach (var existingHandler in handlers)
                {
                    if (!ReferenceEquals(existingHandler, handler))
                    {
                        newHandlers.Add(existingHandler);
                    }
                }
                _handlers.TryUpdate(eventType, newHandlers, handlers);

                _logger.LogDebug("Unsubscribed handler {HandlerType} from event {EventType}", 
                    handler.GetType().Name, eventType.Name);
            }
        }

        /// <summary>
        /// Unsubscribe all handlers for an event type
        /// </summary>
        public void UnsubscribeAll<T>() where T : class, IEvent
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(InMemoryEventBus));

            var eventType = typeof(T);
            _handlers.TryRemove(eventType, out _);

            _logger.LogDebug("Unsubscribed all handlers from event {EventType}", eventType.Name);
        }

        /// <summary>
        /// Get the number of handlers for a specific event type
        /// </summary>
        public int GetHandlerCount<T>() where T : class, IEvent
        {
            var eventType = typeof(T);
            return _handlers.TryGetValue(eventType, out var handlers) ? handlers.Count : 0;
        }

        /// <summary>
        /// Get all registered event types
        /// </summary>
        public IEnumerable<Type> GetRegisteredEventTypes()
        {
            return _handlers.Keys.ToList();
        }

        /// <summary>
        /// Clear all handlers
        /// </summary>
        public void Clear()
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(InMemoryEventBus));

            _handlers.Clear();
            _logger.LogDebug("Cleared all event handlers");
        }

        private async Task ExecuteHandlerAsync<T>(IEventHandler<T> handler, T eventData, CancellationToken cancellationToken) 
            where T : class, IEvent
        {
            try
            {
                await handler.HandleAsync(eventData, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing handler {HandlerType} for event {EventType} with ID {EventId}", 
                    handler.GetType().Name, typeof(T).Name, eventData.EventId);
                throw;
            }
        }

        private async Task ExecuteFuncHandlerAsync<T>(Func<T, CancellationToken, Task> handler, T eventData, CancellationToken cancellationToken) 
            where T : class, IEvent
        {
            try
            {
                await handler(eventData, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing function handler for event {EventType} with ID {EventId}", 
                    typeof(T).Name, eventData.EventId);
                throw;
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _handlers.Clear();
                _publishLock.Dispose();
                _disposed = true;
                _logger.LogDebug("InMemoryEventBus disposed");
            }
        }
    }

    /// <summary>
    /// Event bus extensions for easier registration
    /// </summary>
    public static class EventBusExtensions
    {
        /// <summary>
        /// Subscribe to multiple event types with the same handler
        /// </summary>
        public static void SubscribeToMultiple<THandler>(this IEventBus eventBus, THandler handler, params Type[] eventTypes)
            where THandler : class
        {
            foreach (var eventType in eventTypes)
            {
                var subscribeMethod = typeof(IEventBus).GetMethod(nameof(IEventBus.Subscribe));
                var genericMethod = subscribeMethod?.MakeGenericMethod(eventType);
                genericMethod?.Invoke(eventBus, new object[] { handler });
            }
        }

        /// <summary>
        /// Publish event and ignore exceptions
        /// </summary>
        public static async Task PublishSafeAsync<T>(this IEventBus eventBus, T eventData, ILogger? logger = null, CancellationToken cancellationToken = default) 
            where T : class, IEvent
        {
            try
            {
                await eventBus.PublishAsync(eventData, cancellationToken);
            }
            catch (Exception ex)
            {
                logger?.LogError(ex, "Error publishing event {EventType} with ID {EventId}", 
                    typeof(T).Name, eventData.EventId);
            }
        }

        /// <summary>
        /// Publish event with timeout
        /// </summary>
        public static async Task PublishWithTimeoutAsync<T>(this IEventBus eventBus, T eventData, TimeSpan timeout, CancellationToken cancellationToken = default) 
            where T : class, IEvent
        {
            using var timeoutCts = new CancellationTokenSource(timeout);
            using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, timeoutCts.Token);
            
            await eventBus.PublishAsync(eventData, combinedCts.Token);
        }
    }
}
