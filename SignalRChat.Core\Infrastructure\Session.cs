﻿namespace SignalRChat.Core.Infrastructure
{
    public class Session : ISession
    {
        public SignalRConnectionManagerOptions SignalRConnectionManagerOptions { get; set; }

        private ILogger _logger;
        public ILogger Logger
        {
            get
            {
                if (_logger == null)
                    _logger = Helpers.Logger.CreateSerilog();
                return _logger;
            }
        }

        private UserDto _user;
        public UserDto User
        {
            get { return _user; }
            set
            {
                _user = value;
            }
        }

        private bool _isConnected;
        public bool IsConnected
        {
            get { return _isConnected; }
            set
            {
                _isConnected = value;
            }
        }

        private bool _isLoggedIn;
        public bool IsLoggedIn
        {
            get { return _isLoggedIn; }
            set
            {
                _isLoggedIn = value;
            }
        }

        private BindingList<ChatCard> _chatCards = new BindingList<ChatCard>();
        public BindingList<ChatCard> ChatCards
        {
            get { return _chatCards; }
            set
            {
                _chatCards = value;
            }
        }

        private ChatCard _selectedChatCard;
        public ChatCard SelectedChatCard
        {
            get { return _selectedChatCard; }
            set
            {
                _selectedChatCard = value;
                if (SelectedChatCard.HasSentNewMessage) SelectedChatCard.HasSentNewMessage = false;
            }
        }

        public ChatCard? GetChatCard(Ulid clientId) => ChatCards.SingleOrDefault(x => x.ClientId.Equals(clientId));
    }
}
