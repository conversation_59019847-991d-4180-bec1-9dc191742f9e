﻿namespace SignalRChat.Core.Infrastructure.SendManager
{
    public class SendChatMessageManager : ISendChatMessageManager
    {
        private readonly ISignalRConnectionManager _signalRConnectionManager;
        private readonly ISession _session;
        private readonly ILogger _logger;

        public SendChatMessageManager(ISignalRConnectionManager signalRConnectionManager, ISession session)
        {
            _signalRConnectionManager = signalRConnectionManager;
            _session = session;
            _logger = session.Logger;
        }

        public async Task<bool> SendAsync(IChatMessageRequest? request)
        {
            try
            {
                if (request == null)
                    return false;

                _logger.Information($"[{nameof(SendChatMessageManager)}].[{nameof(SendAsync)}] => Start Sending Message From [{request.SenderId}] To [{request.ReceiverId}]");
                switch (request)
                {
                    case TextMessageRequest textMessageRequest:
                        await _signalRConnectionManager.InvokeCoreAsync(nameof(Hubs.SignalRChatHub.TextMessage), new object[] { request });
                        break;
                    case ImageMessageRequest imageMessageRequest:
                        await _signalRConnectionManager.InvokeCoreAsync(nameof(Hubs.SignalRChatHub.ImageMessage), new object[] { request });
                        break;
                    case RecordMessageRequest recordMessageRequest:
                        await _signalRConnectionManager.InvokeCoreAsync(nameof(Hubs.SignalRChatHub.RecordMessage), new object[] { request });
                        break;
                    case StreamMessageRequest streamMessageRequest:
                        await _signalRConnectionManager.InvokeCoreAsync(nameof(Hubs.SignalRChatHub.StreamMessage), new object[] { request });
                        break;
                    case BuzzMessageRequest buzzMessageRequest:
                        await _signalRConnectionManager.InvokeCoreAsync(nameof(Hubs.SignalRChatHub.BuzzMessage), new object[] { request });
                        break;
                    case ParticipantTypingRequest participantTypingRequest:
                        await _signalRConnectionManager.InvokeCoreAsync(nameof(Hubs.SignalRChatHub.ParticipantTyping), new object[] { request });
                        break;
                    default:
                        _logger.Warning($"[{nameof(SendChatMessageManager)}].[{nameof(SendAsync)}] => Unknown message type");
                        return false;
                }
                _logger.Information($"[{nameof(SendChatMessageManager)}].[{nameof(SendAsync)}] => End Sending Message From [{request.SenderId}] To [{request.ReceiverId}]");
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }
    }
}
