using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using SignalRChat.Core.Configuration;
using System.Reflection;
using System.Runtime.Loader;

namespace SignalRChat.Core.Plugins
{
    /// <summary>
    /// Plugin manager for loading, managing, and executing plugins
    /// </summary>
    public class PluginManager : IPluginManager, IDisposable
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<PluginManager> _logger;
        private readonly PluginOptions _options;
        private readonly Dictionary<string, IPlugin> _loadedPlugins;
        private readonly Dictionary<string, PluginLoadContext> _pluginContexts;
        private readonly SemaphoreSlim _pluginLock;
        private bool _disposed;

        public PluginManager(
            IServiceProvider serviceProvider,
            ILogger<PluginManager> logger,
            IOptions<PluginOptions> options)
        {
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
            _loadedPlugins = new Dictionary<string, IPlugin>();
            _pluginContexts = new Dictionary<string, PluginLoadContext>();
            _pluginLock = new SemaphoreSlim(1, 1);
        }

        /// <summary>
        /// Get all loaded plugins
        /// </summary>
        public IEnumerable<IPlugin> LoadedPlugins => _loadedPlugins.Values;

        /// <summary>
        /// Get plugins of specific type
        /// </summary>
        public IEnumerable<T> GetPlugins<T>() where T : class, IPlugin
        {
            return _loadedPlugins.Values.OfType<T>();
        }

        /// <summary>
        /// Get plugin by ID
        /// </summary>
        public T? GetPlugin<T>(string pluginId) where T : class, IPlugin
        {
            return _loadedPlugins.TryGetValue(pluginId, out var plugin) ? plugin as T : null;
        }

        /// <summary>
        /// Initialize plugin manager
        /// </summary>
        public async Task InitializeAsync(CancellationToken cancellationToken = default)
        {
            if (!_options.Enabled)
            {
                _logger.LogInformation("Plugin system is disabled");
                return;
            }

            _logger.LogInformation("Initializing plugin manager");

            try
            {
                if (_options.AutoLoadOnStartup)
                {
                    await DiscoverAndLoadPluginsAsync(cancellationToken);
                }

                _logger.LogInformation("Plugin manager initialized successfully. Loaded {PluginCount} plugins", _loadedPlugins.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize plugin manager");
                throw;
            }
        }

        /// <summary>
        /// Discover and load plugins from configured paths
        /// </summary>
        public async Task DiscoverAndLoadPluginsAsync(CancellationToken cancellationToken = default)
        {
            await _pluginLock.WaitAsync(cancellationToken);
            try
            {
                var discoveredPlugins = new List<PluginInfo>();

                foreach (var path in _options.DiscoveryPaths)
                {
                    if (Directory.Exists(path))
                    {
                        var plugins = await DiscoverPluginsInPathAsync(path, cancellationToken);
                        discoveredPlugins.AddRange(plugins);
                    }
                    else
                    {
                        _logger.LogWarning("Plugin discovery path does not exist: {Path}", path);
                    }
                }

                // Sort plugins by dependencies
                var sortedPlugins = SortPluginsByDependencies(discoveredPlugins);

                // Load plugins in dependency order
                foreach (var pluginInfo in sortedPlugins)
                {
                    if (ShouldLoadPlugin(pluginInfo.Id))
                    {
                        await LoadPluginAsync(pluginInfo, cancellationToken);
                    }
                }
            }
            finally
            {
                _pluginLock.Release();
            }
        }

        /// <summary>
        /// Load a specific plugin
        /// </summary>
        public async Task<bool> LoadPluginAsync(string pluginPath, CancellationToken cancellationToken = default)
        {
            await _pluginLock.WaitAsync(cancellationToken);
            try
            {
                var pluginInfo = await AnalyzePluginAsync(pluginPath, cancellationToken);
                if (pluginInfo != null && ShouldLoadPlugin(pluginInfo.Id))
                {
                    await LoadPluginAsync(pluginInfo, cancellationToken);
                    return true;
                }
                return false;
            }
            finally
            {
                _pluginLock.Release();
            }
        }

        /// <summary>
        /// Unload a plugin
        /// </summary>
        public async Task<bool> UnloadPluginAsync(string pluginId, CancellationToken cancellationToken = default)
        {
            await _pluginLock.WaitAsync(cancellationToken);
            try
            {
                if (_loadedPlugins.TryGetValue(pluginId, out var plugin))
                {
                    _logger.LogInformation("Unloading plugin: {PluginId}", pluginId);

                    await plugin.StopAsync(cancellationToken);
                    await plugin.DisposeAsync();

                    _loadedPlugins.Remove(pluginId);

                    if (_pluginContexts.TryGetValue(pluginId, out var context))
                    {
                        context.Unload();
                        _pluginContexts.Remove(pluginId);
                    }

                    _logger.LogInformation("Plugin unloaded successfully: {PluginId}", pluginId);
                    return true;
                }

                return false;
            }
            finally
            {
                _pluginLock.Release();
            }
        }

        /// <summary>
        /// Reload a plugin
        /// </summary>
        public async Task<bool> ReloadPluginAsync(string pluginId, CancellationToken cancellationToken = default)
        {
            if (await UnloadPluginAsync(pluginId, cancellationToken))
            {
                // Find the plugin path and reload it
                foreach (var path in _options.DiscoveryPaths)
                {
                    var pluginPath = Path.Combine(path, $"{pluginId}.dll");
                    if (File.Exists(pluginPath))
                    {
                        return await LoadPluginAsync(pluginPath, cancellationToken);
                    }
                }
            }
            return false;
        }

        private async Task<List<PluginInfo>> DiscoverPluginsInPathAsync(string path, CancellationToken cancellationToken)
        {
            var plugins = new List<PluginInfo>();
            var dllFiles = Directory.GetFiles(path, "*.dll", SearchOption.AllDirectories);

            foreach (var dllFile in dllFiles)
            {
                try
                {
                    var pluginInfo = await AnalyzePluginAsync(dllFile, cancellationToken);
                    if (pluginInfo != null)
                    {
                        plugins.Add(pluginInfo);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to analyze potential plugin: {FilePath}", dllFile);
                }
            }

            return plugins;
        }

        private async Task<PluginInfo?> AnalyzePluginAsync(string assemblyPath, CancellationToken cancellationToken)
        {
            try
            {
                var assembly = Assembly.LoadFrom(assemblyPath);
                var pluginTypes = assembly.GetTypes()
                    .Where(t => typeof(IPlugin).IsAssignableFrom(t) && !t.IsInterface && !t.IsAbstract)
                    .ToList();

                if (pluginTypes.Count == 0)
                {
                    return null;
                }

                // For simplicity, take the first plugin type found
                var pluginType = pluginTypes.First();
                var plugin = Activator.CreateInstance(pluginType) as IPlugin;

                if (plugin != null)
                {
                    return new PluginInfo
                    {
                        Id = plugin.Id,
                        Name = plugin.Name,
                        Version = plugin.Version,
                        Description = plugin.Description,
                        Author = plugin.Author,
                        AssemblyPath = assemblyPath,
                        PluginType = pluginType,
                        Dependencies = plugin.Dependencies.ToList()
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to analyze assembly: {AssemblyPath}", assemblyPath);
            }

            return null;
        }

        private List<PluginInfo> SortPluginsByDependencies(List<PluginInfo> plugins)
        {
            var sorted = new List<PluginInfo>();
            var visited = new HashSet<string>();
            var visiting = new HashSet<string>();

            foreach (var plugin in plugins)
            {
                if (!visited.Contains(plugin.Id))
                {
                    VisitPlugin(plugin, plugins, sorted, visited, visiting);
                }
            }

            return sorted;
        }

        private void VisitPlugin(PluginInfo plugin, List<PluginInfo> allPlugins, List<PluginInfo> sorted, HashSet<string> visited, HashSet<string> visiting)
        {
            if (visiting.Contains(plugin.Id))
            {
                throw new InvalidOperationException($"Circular dependency detected for plugin: {plugin.Id}");
            }

            if (visited.Contains(plugin.Id))
            {
                return;
            }

            visiting.Add(plugin.Id);

            foreach (var dependency in plugin.Dependencies.Where(d => !d.IsOptional))
            {
                var dependencyPlugin = allPlugins.FirstOrDefault(p => p.Id == dependency.PluginId);
                if (dependencyPlugin != null)
                {
                    VisitPlugin(dependencyPlugin, allPlugins, sorted, visited, visiting);
                }
                else
                {
                    throw new InvalidOperationException($"Required dependency not found: {dependency.PluginId} for plugin: {plugin.Id}");
                }
            }

            visiting.Remove(plugin.Id);
            visited.Add(plugin.Id);
            sorted.Add(plugin);
        }

        private bool ShouldLoadPlugin(string pluginId)
        {
            if (_options.DisabledPlugins.Contains(pluginId))
            {
                return false;
            }

            if (_options.EnabledPlugins.Count > 0 && !_options.EnabledPlugins.Contains(pluginId))
            {
                return false;
            }

            return true;
        }

        private async Task LoadPluginAsync(PluginInfo pluginInfo, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Loading plugin: {PluginId} v{Version}", pluginInfo.Id, pluginInfo.Version);

                var context = new PluginLoadContext(pluginInfo.AssemblyPath);
                var assembly = context.LoadFromAssemblyPath(pluginInfo.AssemblyPath);
                var plugin = Activator.CreateInstance(pluginInfo.PluginType) as IPlugin;

                if (plugin == null)
                {
                    throw new InvalidOperationException($"Failed to create instance of plugin: {pluginInfo.Id}");
                }

                // Configure services for the plugin
                var services = new ServiceCollection();
                plugin.ConfigureServices(services);

                // Initialize the plugin
                await plugin.InitializeAsync(_serviceProvider, _logger, cancellationToken);

                // Start the plugin
                await plugin.StartAsync(cancellationToken);

                _loadedPlugins[pluginInfo.Id] = plugin;
                _pluginContexts[pluginInfo.Id] = context;

                _logger.LogInformation("Plugin loaded successfully: {PluginId}", pluginInfo.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to load plugin: {PluginId}", pluginInfo.Id);
                throw;
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                foreach (var plugin in _loadedPlugins.Values)
                {
                    try
                    {
                        plugin.StopAsync().GetAwaiter().GetResult();
                        plugin.DisposeAsync().GetAwaiter().GetResult();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error disposing plugin: {PluginId}", plugin.Id);
                    }
                }

                foreach (var context in _pluginContexts.Values)
                {
                    context.Unload();
                }

                _pluginLock.Dispose();
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// Plugin information
    /// </summary>
    public class PluginInfo
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public Version Version { get; set; } = new Version();
        public string Description { get; set; } = string.Empty;
        public string Author { get; set; } = string.Empty;
        public string AssemblyPath { get; set; } = string.Empty;
        public Type PluginType { get; set; } = null!;
        public List<PluginDependency> Dependencies { get; set; } = new();
    }

    /// <summary>
    /// Plugin load context for isolation
    /// </summary>
    public class PluginLoadContext : AssemblyLoadContext
    {
        private readonly AssemblyDependencyResolver _resolver;

        public PluginLoadContext(string pluginPath) : base(isCollectible: true)
        {
            _resolver = new AssemblyDependencyResolver(pluginPath);
        }

        protected override Assembly? Load(AssemblyName assemblyName)
        {
            var assemblyPath = _resolver.ResolveAssemblyToPath(assemblyName);
            return assemblyPath != null ? LoadFromAssemblyPath(assemblyPath) : null;
        }

        protected override IntPtr LoadUnmanagedDll(string unmanagedDllName)
        {
            var libraryPath = _resolver.ResolveUnmanagedDllToPath(unmanagedDllName);
            return libraryPath != null ? LoadUnmanagedDllFromPath(libraryPath) : IntPtr.Zero;
        }
    }
}
