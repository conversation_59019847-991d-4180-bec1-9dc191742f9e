﻿global using Microsoft.Extensions.DependencyInjection;
global using SignalRChat.Core.DI;
global using SignalRChat.Core.Enums;
global using SignalRChat.Core.Helpers;
global using SignalRChat.Core.Infrastructure;
global using SignalRChat.Core.Infrastructure.ConnectionManager;
global using SignalRChat.Core.Models;
global using SignalRChat.Core.Modules.ChatMessage.BuzzMessage.Services;
global using SignalRChat.Core.Modules.ChatMessage.BuzzMessage.ViewModel;
global using SignalRChat.Core.Modules.ChatMessage.ImageMessage.Services;
global using SignalRChat.Core.Modules.ChatMessage.ParticipantTyping.Services;
global using SignalRChat.Core.Modules.ChatMessage.RecordMessage.Services;
global using SignalRChat.Core.Modules.ChatMessage.StreamMessage.Services;
global using SignalRChat.Core.Modules.ChatMessage.TextMessage.Services;
global using SignalRChat.Core.Modules.CreateGroup.Services;
global using SignalRChat.Core.Modules.JoinGroup.Services;
global using SignalRChat.Core.Modules.LeaveGroup.Services;
global using SignalRChat.Core.Modules.Login.Services;
global using SignalRChat.Core.Modules.Logout.Services;
global using SignalRChat.Core.Strategies.Messages;
global using SignalRChat.Core.Strategies.Messages.ImageMessage;
global using SignalRChat.Core.Strategies.Messages.RecordMessage;
global using SignalRChat.Core.Strategies.Messages.StreamMessage;
global using SignalRChat.Core.Strategies.Messages.TextMessage;
global using SignalRChat.Core.ViewModels;
global using SignalRChat.CustomControls;
global using SignalRChat.DI;
global using SignalRChat.Extensions;
global using SignalRChat.Factories.Messages;
global using SignalRChat.Helpers;
global using SignalRChat.Strategies.Messages;
global using SignalRChat.Views;
global using System.ComponentModel;
global using System.Diagnostics;
global using System.Drawing.Drawing2D;
global using System.Drawing.Text;
global using System.Media;
global using System.Runtime.CompilerServices;
