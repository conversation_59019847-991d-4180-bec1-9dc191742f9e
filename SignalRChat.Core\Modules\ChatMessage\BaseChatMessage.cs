﻿namespace SignalRChat.Core.Modules.ChatMessage
{
    public class BaseChatMessage
    {
        protected readonly ISignalRConnectionManager _signalRConnectionManager;
        protected readonly ISendChatMessageManager _sendChatMessageManager;
        protected readonly IReceiveChatMessageManager _receiveChatMessageManager;
        protected readonly ISession _session;
        protected readonly ILogger _logger;

        public BaseChatMessage(
            ISignalRConnectionManager signalRConnectionManager,
            ISendChatMessageManager sendChatMessageManager,
            IReceiveChatMessageManager receiveChatMessageManager,
            ISession session)
        {
            _signalRConnectionManager = signalRConnectionManager;
            _sendChatMessageManager = sendChatMessageManager;
            _receiveChatMessageManager = receiveChatMessageManager;
            _session = session;
            _logger = session.Logger;
        }

        protected bool CanSend(IChatMessageRequest? request)
        {
            return
                request != null
                && request.IsValid()
                && _session.CanSendMessage;
        }

        protected async Task<bool> SendAsync(IChatMessageRequest request)
        {
            if (request is null)
                return false;

            return await _sendChatMessageManager.SendAsync(request);
        }

        protected async Task<bool> ReceiveAsync(IChatMessageResponse? response)
        {
            if (response is null)
                return false;

            return await _receiveChatMessageManager.ReceiveAsync(response);
        }
    }
}
