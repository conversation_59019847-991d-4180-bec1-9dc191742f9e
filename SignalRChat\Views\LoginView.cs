using Microsoft.Extensions.Configuration;

namespace SignalRChat.Views
{
    public partial class LoginView : BaseView, IDisposable
    {
        private readonly ChatView _chatView;
        private readonly ILoginService _loginService;
        private readonly string _url;

        public LoginView(ILoginService loginService, ChatView chatView)
        {
            InitializeComponent();

            _loginService = loginService ?? throw new ArgumentNullException(nameof(loginService));
            _chatView = chatView ?? throw new ArgumentNullException(nameof(chatView));

            // Load configuration
            var config = new ConfigurationBuilder()
                .SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
                .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
                .Build();
            _url = config["SignalR:Url"] ?? "http://localhost:8080/signalchat";

            pbxProfilePicture.Click += PbxProfilePicture_Click;
            btnLogin.Click += BtnLogin_Click;

            if (Debugger.IsAttached)
            {
                string processName = Process.GetCurrentProcess().ProcessName;
                int instanceCount = Process.GetProcessesByName(processName).Length;
                txtUserName.Text = instanceCount.ToString();
                _ = Login(); // Suppress warning: fire-and-forget
            }
        }

        public LoginView(string url, string username, Image photo) : base()
        {
            _url = url ?? throw new ArgumentNullException(nameof(url));
            txtUserName.Text = username ?? throw new ArgumentNullException(nameof(username));
            pbxProfilePicture.Image = photo ?? throw new ArgumentNullException(nameof(photo));
        }

        private async void BtnLogin_Click(object? sender, EventArgs e)
        {
            await Login();
        }

        private async Task Login()
        {
            SignalRConnectionManagerOptions signalRConnectionManagerOptions = new SignalRConnectionManagerOptions
            {
                UserId = UlidGenerator.Generator(txtUserName.Text),
                Url = _url,
                UserName = txtUserName.Text,
                Photo = Converters.ImageToByteArray(pbxProfilePicture.Image),
            };
            signalRConnectionManagerOptions.Headers.Add("UserName", signalRConnectionManagerOptions.UserId.ToString());
            var result = await _loginService.SendAsync(signalRConnectionManagerOptions);
            if (result)
            {
                _chatView.Show();
                this.Hide();
            }
        }

        private void PbxProfilePicture_Click(object? sender, EventArgs e)
        {
            SelectImage();
        }

        private void SelectImage()
        {
            string selectedImage = DialogBoxUtilities.OpenFile("Select image file", "Images (*.jpg;*.png)|*.jpg;*.png");
            if (!string.IsNullOrEmpty(selectedImage))
                pbxProfilePicture.Image = Image.FromFile(selectedImage);
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                // Unsubscribe from events
                pbxProfilePicture.Click -= PbxProfilePicture_Click;
                btnLogin.Click -= BtnLogin_Click;

                // Dispose managed resources
                components?.Dispose();
            }

            if (disposing && (components != null))
            {
                components.Dispose();
            }

            base.Dispose(disposing);
        }
    }
}
