﻿namespace SignalRChat.Core.Models
{
    public class Client
    {
        // Parameterless constructor for EF Core
        internal Client() { }

        public Client(string connectionId, string name, byte[] photo, ChatType chatType)
        {
            ClientId = Ulid.NewUlid();
            ConnectionId = connectionId;
            Name = name;
            Photo = photo;
            ChatType = chatType;
        }

        public Client(ClientDto clientDto)
        {
            ClientId = clientDto.ClientId;
            ConnectionId = clientDto.ConnectionId;
            Name = clientDto.Name;
            Photo = clientDto.Photo;
            ChatType = clientDto.ChatType;
        }

        public Ulid ClientId { get; private set; }
        public string ConnectionId { get; private set; }
        public string Name { get; private set; }
        public byte[] Photo { get; private set; }
        public ChatType ChatType { get; private set; }

        public ICollection<ChatMessage> SentMessages { get; set; } = new List<ChatMessage>();
        public ICollection<ChatMessage> ReceivedMessages { get; set; } = new List<ChatMessage>();

        public void UpdateConnectionId(string connectionId) => ConnectionId = connectionId;
    }

    public class User : Client
    {
        // Parameterless constructor for EF Core
        private User() { }

        public User(string connectionId, string name, byte[] photo) : base(connectionId, name, photo, ChatType.IndividualChat)
        {
        }

        public User(UserDto userDto) : base(userDto)
        {
        }

        public string UserName => base.Name;
    }

    public class Group : Client
    {
        // Parameterless constructor for EF Core
        private Group() { }

        public Group(string connectionId, string name, byte[] photo, ICollection<User> users) : base(connectionId, name, photo, ChatType.GroupChat)
        {
            Users = users;
        }

        public Group(GroupDto groupDto) : base(groupDto)
        {
        }

        public ICollection<User> Users { get; private set; }
    }
}
