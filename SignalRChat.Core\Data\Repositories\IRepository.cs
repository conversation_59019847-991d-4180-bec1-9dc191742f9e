﻿namespace SignalRChat.Core.Data.Repositories
{
    public interface IRepository : IRepository<UserDto, GroupDto>
    {
    }

    public interface IRepository<TUser, TGroup>
        where TUser : UserDto
        where TGroup : GroupDto
    {
        Task<List<TDto>> GetClientsByType<T, TDto>() where T : Client where TDto : ClientDto;
        Task<TDto?> GetClientByType<T, TDto>(Ulid clientId) where T : Client where TDto : ClientDto;
        Task<TDto?> GetClientWithMessagesByType<T, TDto>(Ulid clientId) where T : Client where TDto : ClientDto;

        Task<List<TUser>> GetUsersWithMessages();
        Task<List<TGroup>> GetGroups();
        Task<List<TGroup>> GetGroupsWithMessages();

        Task<bool> AddClient(ClientDto client);
        Task<bool> UpdateClient(ClientDto client);
        Task<bool> DeleteClient(Ulid clientId);

        Task<bool> AddMessage(IChatMessageResponse response);
    }
}
