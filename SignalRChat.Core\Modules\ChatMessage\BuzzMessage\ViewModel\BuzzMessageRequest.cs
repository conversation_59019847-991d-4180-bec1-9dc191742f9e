﻿namespace SignalRChat.Core.Modules.ChatMessage.BuzzMessage.ViewModel
{
    public record BuzzMessageRequest : BaseChatMessageRequest
    {
        [JsonConstructor]
        public BuzzMessageRequest(Ulid messageId, Ulid senderId, Ulid receiverId) : base(messageId, senderId, receiverId)
        {
        }

        public override bool IsValid()
        {
            return
                base.IsValid();
        }
    }
}
