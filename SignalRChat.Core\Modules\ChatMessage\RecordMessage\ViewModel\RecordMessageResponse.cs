﻿namespace SignalRChat.Core.Modules.ChatMessage.RecordMessage.ViewModel
{
    public record RecordMessageResponse : BaseChatMessageResponse
    {
        [JsonConstructor]
        public RecordMessageResponse(Ulid messageId, Ulid senderId, Ulid receiverId, byte[] record) : base(messageId, senderId, receiverId, true)
        {
            Record = record;
        }

        public RecordMessageResponse(RecordMessageRequest request) : this(request.MessageId, request.SenderId, request.ReceiverId, request.Record)
        {
        }

        [JsonInclude]
        public byte[] Record { get; private set; }
    }
}
