﻿namespace SignalRChat.Core.Modules.ChatMessage
{
    public record BaseChatMessageRequest : IChatMessageRequest
    {
        [JsonConstructor]
        protected BaseChatMessageRequest(Ulid messageId, Ulid senderId, Ulid receiverId)
        {
            MessageId = messageId;
            SenderId = senderId;
            ReceiverId = receiverId;
        }

        [JsonInclude]
        public Ulid MessageId { get; private set; }
        [JsonInclude]
        public Ulid SenderId { get; private set; }
        [JsonInclude]
        public Ulid ReceiverId { get; private set; }

        public virtual bool IsValid()
        {
            return
                MessageId.IsValid()
                && SenderId.IsValid()
                && ReceiverId.IsValid();
        }
    }
}
