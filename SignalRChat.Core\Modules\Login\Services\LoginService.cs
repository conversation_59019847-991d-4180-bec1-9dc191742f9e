﻿namespace SignalRChat.Core.Modules.Login.Services
{
    public class LoginService : ILoginService
    {
        private readonly ISignalRConnectionManager _signalRConnectionManager;
        private readonly ISession _session;
        private readonly ILogger _logger;

        public event LoginReceivedEventHandler OnNewLogin;

        public LoginService(ISignalRConnectionManager signalRConnectionManager, ISession session)
        {
            _signalRConnectionManager = signalRConnectionManager;
            _session = session;
            _logger = session.Logger;

            _signalRConnectionManager.OnLoginReceived += Receive;
        }

        public async Task<bool> SendAsync(SignalRConnectionManagerOptions signalRConnectionManagerOptions)
        {
            var result = await ConnectAsync(signalRConnectionManagerOptions);
            if (result)
            {
                LoginRequest request = new LoginRequest()
                {
                    Id = signalRConnectionManagerOptions.UserId,
                    Name = signalRConnectionManagerOptions.UserName,
                    Photo = signalRConnectionManagerOptions.Photo,
                };
                if (CanSend(request))
                    return await SendAsync(request);
            }
            return false;
        }

        public bool CanSend(LoginRequest? request)
        {
            return
                request != null
                && !string.IsNullOrEmpty(request.Name)
                && _session.IsConnected;
        }

        public async Task<bool> SendAsync(LoginRequest request)
        {
            try
            {
                _logger.Information($"[{nameof(LoginService)}].[{nameof(SendAsync)}] => Start Login");
                _logger.Information($"[{nameof(LoginService)}].[{nameof(SendAsync)}] => Login As [{request.Name}]");
                var login = await _signalRConnectionManager.LoginAsync(request);
                if (login != null)
                {
                    _session.User = login.User;
                    if (login.Users != null)
                    {
                        login.Users.ToList().ForEach(client => _session.ChatCards.TryAdd(new ChatCard(client)));
                        _logger.Information($"[{nameof(LoginService)}].[{nameof(SendAsync)}] => End Login");
                        return true;
                    }
                    else
                        throw new Exception("Username is already in use");
                }
                else
                    throw new Exception("There error on logging in");
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        public void Receive(LoginResponse? response)
        {
            if (response is null)
                return;

            OnNewLogin?.Invoke(response);
        }

        public async Task<bool> ConnectAsync(SignalRConnectionManagerOptions signalRConnectionManagerOptions)
        {
            try
            {
                _logger.Information($"[{nameof(LoginService)}].[{nameof(ConnectAsync)}] => Start Server Connection");
                await _signalRConnectionManager.Subscribe(signalRConnectionManagerOptions);
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

    }
}
