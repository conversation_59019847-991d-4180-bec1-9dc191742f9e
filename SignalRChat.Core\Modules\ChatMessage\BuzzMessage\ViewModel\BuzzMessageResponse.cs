﻿namespace SignalRChat.Core.Modules.ChatMessage.BuzzMessage.ViewModel
{
    public record BuzzMessageResponse : BaseChatMessageResponse
    {
        [JsonConstructor]
        public BuzzMessageResponse(Ulid messageId, Ulid senderId, Ulid receiverId) : base(messageId, senderId, receiverId, false)
        {
        }

        public BuzzMessageResponse(BuzzMessageRequest request) : this(request.MessageId, request.SenderId, request.ReceiverId)
        {
        }
    }
}
