﻿namespace SignalRChat.Core.Modules.ChatMessage.ParticipantTyping.Services
{
    public class ParticipantTypingService : BaseChatMessage, IParticipantTypingService
    {
        public event ParticipantTypingReceivedEventHandler OnParticipantTyping;

        public ParticipantTypingService(
            ISignalRConnectionManager signalRConnectionManager,
            ISendChatMessageManager sendChatMessageManager,
            IReceiveChatMessageManager receiveChatMessageManager,
            ISession session) : base(signalRConnectionManager, sendChatMessageManager, receiveChatMessageManager, session)
        {
            _signalRConnectionManager.OnParticipantTypingReceived += Receive;
        }

        public async Task<bool> SendAsync(Ulid recipientId)
        {
            ParticipantTypingRequest request = new ParticipantTypingRequest(
                UlidGenerator.Generator(),
                _session.User.ClientId,
                recipientId
            );

            if (CanSend(request))
                return await SendAsync(request);
            return false;
        }

        public bool CanSend(ParticipantTypingRequest? request)
        {
            return base.CanSend(request);
        }

        public async Task<bool> SendAsync(ParticipantTypingRequest request)
        {
            return await _sendChatMessageManager.SendAsync(request);
        }

        public async void Receive(ParticipantTypingResponse? response)
        {
            if (response is null)
                return;

            await _receiveChatMessageManager.ReceiveAsync(response);
            OnParticipantTyping?.Invoke(response);
        }
    }
}
