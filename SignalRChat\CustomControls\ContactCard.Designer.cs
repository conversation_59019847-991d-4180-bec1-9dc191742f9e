﻿namespace SignalRChat.CustomControls
{
    partial class ContactCard
    {
        /// <summary> 
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        #region Component Designer generated code

        /// <summary> 
        /// Required method for Designer support - do not modify 
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            tlpMain = new TableLayoutPanel();
            pbxIsSelected = new PictureBox();
            pbxIsTypingOrHasSentNewMessage = new PictureBox();
            pbxIsOnline = new PictureBox();
            pbxPhoto = new PictureBox();
            lblName = new Label();
            tlpMain.SuspendLayout();
            ((ISupportInitialize)pbxIsSelected).BeginInit();
            ((ISupportInitialize)pbxIsTypingOrHasSentNewMessage).BeginInit();
            ((ISupportInitialize)pbxIsOnline).BeginInit();
            ((ISupportInitialize)pbxPhoto).BeginInit();
            SuspendLayout();
            // 
            // tlpMain
            // 
            tlpMain.ColumnCount = 5;
            tlpMain.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 10F));
            tlpMain.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 35F));
            tlpMain.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            tlpMain.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 35F));
            tlpMain.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 35F));
            tlpMain.Controls.Add(pbxIsSelected, 0, 0);
            tlpMain.Controls.Add(pbxIsTypingOrHasSentNewMessage, 3, 0);
            tlpMain.Controls.Add(pbxIsOnline, 4, 0);
            tlpMain.Controls.Add(pbxPhoto, 1, 0);
            tlpMain.Controls.Add(lblName, 2, 0);
            tlpMain.Dock = DockStyle.Fill;
            tlpMain.Location = new Point(0, 0);
            tlpMain.Name = "tlpMain";
            tlpMain.RowCount = 1;
            tlpMain.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            tlpMain.RowStyles.Add(new RowStyle(SizeType.Absolute, 20F));
            tlpMain.Size = new Size(193, 34);
            tlpMain.TabIndex = 0;
            // 
            // pbxIsSelected
            // 
            pbxIsSelected.Dock = DockStyle.Fill;
            pbxIsSelected.Location = new Point(3, 3);
            pbxIsSelected.Name = "pbxIsSelected";
            pbxIsSelected.Size = new Size(4, 28);
            pbxIsSelected.TabIndex = 0;
            pbxIsSelected.TabStop = false;
            // 
            // pbxIsTypingOrHasSentNewMessage
            // 
            pbxIsTypingOrHasSentNewMessage.Dock = DockStyle.Fill;
            pbxIsTypingOrHasSentNewMessage.Location = new Point(126, 3);
            pbxIsTypingOrHasSentNewMessage.Name = "pbxIsTypingOrHasSentNewMessage";
            pbxIsTypingOrHasSentNewMessage.Size = new Size(29, 28);
            pbxIsTypingOrHasSentNewMessage.SizeMode = PictureBoxSizeMode.StretchImage;
            pbxIsTypingOrHasSentNewMessage.TabIndex = 0;
            pbxIsTypingOrHasSentNewMessage.TabStop = false;
            // 
            // pbxIsOnline
            // 
            pbxIsOnline.Dock = DockStyle.Fill;
            pbxIsOnline.Location = new Point(161, 3);
            pbxIsOnline.Name = "pbxIsOnline";
            pbxIsOnline.Size = new Size(29, 28);
            pbxIsOnline.TabIndex = 0;
            pbxIsOnline.TabStop = false;
            // 
            // pbxPhoto
            // 
            pbxPhoto.Dock = DockStyle.Fill;
            pbxPhoto.Location = new Point(13, 3);
            pbxPhoto.Name = "pbxPhoto";
            pbxPhoto.Size = new Size(29, 28);
            pbxPhoto.SizeMode = PictureBoxSizeMode.StretchImage;
            pbxPhoto.TabIndex = 0;
            pbxPhoto.TabStop = false;
            // 
            // lblName
            // 
            lblName.AutoSize = true;
            lblName.Dock = DockStyle.Fill;
            lblName.Location = new Point(48, 0);
            lblName.Name = "lblName";
            lblName.Size = new Size(72, 34);
            lblName.TabIndex = 1;
            lblName.Text = "label1";
            lblName.TextAlign = ContentAlignment.MiddleLeft;
            // 
            // ContactCard
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            Controls.Add(tlpMain);
            Name = "ContactCard";
            Size = new Size(193, 34);
            tlpMain.ResumeLayout(false);
            tlpMain.PerformLayout();
            ((ISupportInitialize)pbxIsSelected).EndInit();
            ((ISupportInitialize)pbxIsTypingOrHasSentNewMessage).EndInit();
            ((ISupportInitialize)pbxIsOnline).EndInit();
            ((ISupportInitialize)pbxPhoto).EndInit();
            ResumeLayout(false);
        }

        #endregion

        private TableLayoutPanel tlpMain;
        private PictureBox pbxIsSelected;
        private PictureBox pbxIsTypingOrHasSentNewMessage;
        private PictureBox pbxIsOnline;
        private PictureBox pbxPhoto;
        private Label lblName;
    }
}
