﻿namespace SignalRChat.Core.Modules.ChatMessage.TextMessage.Services
{
    public class TextMessageService : BaseChatMessage, ITextMessageService
    {
        public event TextMessageReceivedEventHandler OnNewTextMessage;

        public TextMessageService(
            ISignalRConnectionManager signalRConnectionManager,
            ISendChatMessageManager sendChatMessageManager,
            IReceiveChatMessageManager receiveChatMessageManager,
            ISession session) : base(signalRConnectionManager, sendChatMessageManager, receiveChatMessageManager, session)
        {
            _signalRConnectionManager.OnTextMessageReceived += Receive;
        }

        public async Task<bool> SendAsync(string message)
        {
            TextMessageRequest request = new TextMessageRequest(
                UlidGenerator.Generator(message),
                _session.User.ClientId,
                _session.SelectedChatCard.ClientId,
                message
            );

            if (CanSend(request))
                return await SendAsync(request);
            return false;
        }

        public bool CanSend(TextMessageRequest? request)
        {
            return base.CanSend(request);
        }

        public async Task<bool> SendAsync(TextMessageRequest request)
        {
            bool result = await _sendChatMessageManager.SendAsync(request);
            if (result)
            {
                TextChatMessageDto textChatMessageDto = new TextChatMessageDto(request);
                _session.SelectedChatCard.ChatMessages.Add(textChatMessageDto);
            }
            return result;
        }

        public async void Receive(TextMessageResponse? response)
        {
            if (response is null)
                return;

            await _receiveChatMessageManager.ReceiveAsync(response);
            OnNewTextMessage?.Invoke(response);
        }
    }
}
