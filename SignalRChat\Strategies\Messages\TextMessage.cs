﻿namespace SignalRChat.Strategies.Messages
{
    public class TextMessage : Label, ITextMessageView, IDisposable
    {
        public TextMessage()
        {
            this.SafelyInvokeAction(() =>
            {
                Dock = DockStyle.Fill;
                AutoSize = true;
            });
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                // Dispose managed resources if any
            }

            base.Dispose(disposing);
        }
    }
}
