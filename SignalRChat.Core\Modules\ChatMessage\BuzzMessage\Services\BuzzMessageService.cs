﻿namespace SignalRChat.Core.Modules.ChatMessage.BuzzMessage.Services
{
    public class BuzzMessageService : BaseChatMessage, IBuzzMessageService
    {
        public event BuzzMessageReceivedEventHandler OnBuzzMessage;

        public BuzzMessageService(
            ISignalRConnectionManager signalRConnectionManager,
            ISendChatMessageManager sendChatMessageManager,
            IReceiveChatMessageManager receiveChatMessageManager,
            ISession session) : base(signalRConnectionManager, sendChatMessageManager, receiveChatMessageManager, session)
        {
            _signalRConnectionManager.OnBuzzMessageReceived += Receive;
        }

        public async Task<bool> SendAsync(Ulid recipientId)
        {
            BuzzMessageRequest request = new BuzzMessageRequest(
                UlidGenerator.Generator(),
                _session.User.ClientId,
                recipientId);

            if (CanSend(request))
                return await SendAsync(request);
            return false;
        }

        public bool CanSend(BuzzMessageRequest? request)
        {
            return base.CanSend(request);
        }

        public async Task<bool> SendAsync(BuzzMessageRequest request)
        {
            return await _sendChatMessageManager.SendAsync(request);
        }

        public async void Receive(BuzzMessageResponse? response)
        {
            if (response is null)
                return;

            await _receiveChatMessageManager.ReceiveAsync(response);
            OnBuzzMessage?.Invoke(response);
        }
    }
}
