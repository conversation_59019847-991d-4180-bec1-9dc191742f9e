﻿namespace SignalRChat.Core.Infrastructure.ReceiveManager
{
    public class ReceiveChatMessageManager : IReceiveChatMessageManager
    {
        private readonly ISession _session;
        private readonly ILogger _logger;

        public ReceiveChatMessageManager(ISession session)
        {
            _session = session;
            _logger = session.Logger;
        }

        public async Task<bool> ReceiveAsync(IChatMessageResponse? response)
        {
            try
            {
                if (response == null)
                    return false;

                ChatCard? sender = _session.GetChatCard(response.SenderId);
                if (sender != null)
                {
                    _logger.Information($"[{nameof(RecordMessageService)}].[{nameof(ReceiveAsync)}] => Start Receiving Message From [{response.SenderId}]");
                    bool notifyReciver = true;
                    switch (response)
                    {
                        case TextMessageResponse textMessageResponse:
                            TextChatMessageDto textChatMessageDto = new TextChatMessageDto(textMessageResponse);
                            sender.ChatMessages.Add(textChatMessageDto);
                            break;
                        case ImageMessageResponse imageMessageResponse:
                            ImageChatMessageDto imageChatMessageDto = new ImageChatMessageDto(imageMessageResponse);
                            sender.ChatMessages.Add(imageChatMessageDto);
                            break;
                        case RecordMessageResponse recordMessageResponse:
                            RecordChatMessageDto recordChatMessageDto = new RecordChatMessageDto(recordMessageResponse);
                            sender.ChatMessages.Add(recordChatMessageDto);
                            break;
                        case StreamMessageResponse streamMessageResponse:
                            StreamChatMessageDto streamChatMessageDto = new StreamChatMessageDto(streamMessageResponse);
                            streamChatMessageDto.Stream = streamMessageResponse.Stream;
                            var message = sender.ChatMessages.OfType<StreamChatMessageDto>().SingleOrDefault(x => x.SenderId == response.SenderId && x.StreamId == streamChatMessageDto.StreamId);
                            if (message != null)
                                message.Stream = streamChatMessageDto.Stream;
                            else
                                sender.ChatMessages.Add(streamChatMessageDto);
                            break;
                        case BuzzMessageResponse buzzMessageResponse:
                            notifyReciver = false;
                            break;
                        case ParticipantTypingResponse participantTypingResponse:
                            if (!sender.IsTyping)
                            {
                                sender.IsTyping = true;
                                await Task.Delay(1500);
                                sender.IsTyping = false;
                            }
                            notifyReciver = false;
                            break;
                        default:
                            _logger.Warning($"[{nameof(ReceiveChatMessageManager)}].[{nameof(ReceiveAsync)}] => Unknown message type");
                            return false;
                    }
                    if (!(_session.SelectedChatCard != null && sender.ClientId.Equals(_session.SelectedChatCard.ClientId)) && notifyReciver)
                        sender.HasSentNewMessage = true;
                    _logger.Information($"[{nameof(RecordMessageService)}].[{nameof(ReceiveAsync)}] => End Receiving Message From [{response.SenderId}]");
                }
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }
    }
}
