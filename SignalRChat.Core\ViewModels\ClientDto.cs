﻿namespace SignalRChat.Core.ViewModels
{
    [JsonDerivedType(typeof(UserDto), nameof(UserDto.UserName))]
    [JsonDerivedType(typeof(GroupDto), nameof(GroupDto.Users))]
    public class ClientDto
    {
        [JsonConstructor]
        public ClientDto(Ulid clientId, string connectionId, string name, byte[] photo, ChatType chatType)
        {
            ClientId = clientId;
            ConnectionId = connectionId;
            Name = name;
            Photo = photo;
            ChatType = chatType;
        }

        public ClientDto(Client client) : this(client.ClientId, client.ConnectionId, client.Name, client.Photo, client.ChatType)
        {
            SentMessages = client.SentMessages.Select(chatMessage =>
            {
                switch (chatMessage)
                {
                    case TextChatMessage textChatMessage:
                        return new TextChatMessageDto(textChatMessage);
                    case ImageChatMessage imageChatMessage:
                        return new ImageChatMessageDto(imageChatMessage);
                    case RecordChatMessage recordChatMessage:
                        return new RecordChatMessageDto(recordChatMessage);
                    case StreamChatMessage streamChatMessage:
                        return new StreamChatMessageDto(streamChatMessage);
                    default:
                        return new ChatMessageDto(chatMessage);
                }
            }).ToList();
            ReceivedMessages = client.ReceivedMessages.Select(chatMessage =>
            {
                switch (chatMessage)
                {
                    case TextChatMessage textChatMessage:
                        return new TextChatMessageDto(textChatMessage);
                    case ImageChatMessage imageChatMessage:
                        return new ImageChatMessageDto(imageChatMessage);
                    case RecordChatMessage recordChatMessage:
                        return new RecordChatMessageDto(recordChatMessage);
                    case StreamChatMessage streamChatMessage:
                        return new StreamChatMessageDto(streamChatMessage);
                    default:
                        return new ChatMessageDto(chatMessage);
                }
            }).ToList();
        }

        [JsonInclude]
        public Ulid ClientId { get; private set; }
        [JsonInclude]
        public string ConnectionId { get; private set; }
        [JsonInclude]
        public string Name { get; private set; }
        [JsonInclude]
        public byte[] Photo { get; private set; }
        [JsonInclude]
        public ChatType ChatType { get; private set; }

        [JsonInclude]
        public ICollection<ChatMessageDto> SentMessages { get; set; } = new List<ChatMessageDto>();
        [JsonInclude]
        public ICollection<ChatMessageDto> ReceivedMessages { get; set; } = new List<ChatMessageDto>();

        public void UpdateConnectionId(string connectionId) => ConnectionId = connectionId;
    }

    public class UserDto : ClientDto
    {
        [JsonConstructor]
        public UserDto(Ulid clientId, string connectionId, string name, byte[] photo) : base(clientId, connectionId, name, photo, ChatType.IndividualChat)
        {
        }

        public UserDto(User user) : base(user)
        {
        }

        public string UserName => base.Name;
    }

    public class GroupDto : ClientDto
    {
        [JsonConstructor]
        public GroupDto(Ulid clientId, string connectionId, string name, byte[] photo, ICollection<UserDto> users) : base(clientId, connectionId, name, photo, ChatType.GroupChat)
        {
            Users = users;
        }

        public GroupDto(Group group) : base(group)
        {
            Users = group.Users.Select(user => new UserDto(user)).ToList();
        }

        [JsonInclude]
        public ICollection<UserDto> Users { get; set; } = new List<UserDto>();
    }
}
