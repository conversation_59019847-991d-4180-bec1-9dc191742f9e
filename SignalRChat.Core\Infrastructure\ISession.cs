﻿namespace SignalRChat.Core.Infrastructure
{
    public interface ISession
    {
        SignalRConnectionManagerOptions SignalRConnectionManagerOptions { get; set; }
        ILogger Logger { get; }
        UserDto User { get; set; }
        bool IsConnected { get; set; }
        bool IsLoggedIn { get; set; }
        BindingList<ChatCard> ChatCards { get; set; }
        ChatCard SelectedChatCard { get; set; }

        bool IsOnLine => IsConnected && IsLoggedIn;
        bool IsChatCardValid => SelectedChatCard != null && SelectedChatCard.IsLoggedIn;
        bool CanSendMessage => IsOnLine && IsChatCardValid;

        ChatCard? GetChatCard(Ulid clientId);
    }
}
