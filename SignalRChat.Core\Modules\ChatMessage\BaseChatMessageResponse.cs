﻿namespace SignalRChat.Core.Modules.ChatMessage
{
    public record BaseChatMessageResponse : IChatMessageResponse
    {
        [JsonConstructor]
        protected BaseChatMessageResponse(Ulid messageId, Ulid senderId, Ulid receiverId, bool savable)
        {
            MessageId = messageId;
            SenderId = senderId;
            ReceiverId = receiverId;
            Savable = savable;
        }

        [JsonInclude]
        public Ulid MessageId { get; private set; }
        [JsonInclude]
        public Ulid SenderId { get; private set; }
        [JsonInclude]
        public Ulid ReceiverId { get; private set; }
        [JsonIgnore]
        public bool Savable { get; private set; }
    }
}
