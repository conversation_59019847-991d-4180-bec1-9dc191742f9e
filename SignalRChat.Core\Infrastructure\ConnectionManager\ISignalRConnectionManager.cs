﻿namespace SignalRChat.Core.Infrastructure.ConnectionManager
{
    public interface ISignalRConnectionManager
    {
        event Action<UserDto> OnParticipantLoggedIn;
        event Action<Ulid> OnParticipantLoggedOut;
        event Action<Ulid> OnParticipantDisconnected;
        event Action<Ulid> OnParticipantReconnected;
        event Action OnConnectionReconnecting;
        event Action OnConnectionReconnected;
        event Action OnConnectionClosed;

        event LoginReceivedEventHandler OnLoginReceived;
        event LogoutReceivedEventHandler OnLogoutReceived;

        event ParticipantTypingReceivedEventHandler OnParticipantTypingReceived;
        event TextMessageReceivedEventHandler OnTextMessageReceived;
        event ImageMessageReceivedEventHandler OnImageMessageReceived;
        event RecordMessageReceivedEventHandler OnRecordMessageReceived;
        event StreamMessageReceivedEventHandler OnStreamMessageReceived;
        event BuzzMessageReceivedEventHandler OnBuzzMessageReceived;
        event CreateGroupReceivedEventHandler OnCreateGroupReceived;
        event JoinGroupReceivedEventHandler OnJoinGroupReceived;
        event LeaveGroupReceivedEventHandler OnLeaveGroupReceived;

        bool IsConnected { get; }

        Task Subscribe(SignalRConnectionManagerOptions signalRConnectionManagerOptions);
        Task InvokeCoreAsync(string method, params object[] args);
        Task<T> InvokeCoreAsync<T>(string method, params object[] args);
        Task<LoginResponse?> LoginAsync(LoginRequest request);
        Task LogoutAsync(LogoutRequest request);
    }
}
