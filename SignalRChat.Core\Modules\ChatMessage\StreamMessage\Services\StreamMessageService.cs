﻿namespace SignalRChat.Core.Modules.ChatMessage.StreamMessage.Services
{
    public class StreamMessageService : BaseChatMessage, IStreamMessageService
    {
        private SemaphoreSlim locker = new SemaphoreSlim(1);

        public event StreamMessageReceivedEventHandler OnNewStreamMessage;
        public bool IsStreaming { get; set; } = false;

        public StreamMessageService(
            ISignalRConnectionManager signalRConnectionManager,
            ISendChatMessageManager sendChatMessageManager,
            IReceiveChatMessageManager receiveChatMessageManager,
            ISession session) : base(signalRConnectionManager, sendChatMessageManager, receiveChatMessageManager, session)
        {
            _signalRConnectionManager.OnStreamMessageReceived += Receive;
        }

        public async Task<bool> SendAsync()
        {
            IsStreaming = true;
            StreamMessageRequest request = new StreamMessageRequest(
                UlidGenerator.Generator(),
                _session.User.ClientId,
                _session.SelectedChatCard.ClientId,
                UlidGenerator.Generator()
            );

            StreamChatMessageDto streamChatMessageDto = new StreamChatMessageDto(request);
            _session.SelectedChatCard.ChatMessages.Add(streamChatMessageDto);

            await Task.Run(async () =>
            {
                while (IsStreaming)
                {
                    await locker.WaitAsync();
                    try
                    {
                        ScreenShot screenShot = new ScreenShot();
                        screenShot = screenShot.GerScreenShot(ScreenCapture.CaptureDesktop());

                        request.Stream = screenShot.ScreenShotBitmapArray;

#pragma warning disable CS4014 // Because this call is not awaited, execution of the current method continues before the call is completed
                        SendAsync(request);
#pragma warning restore CS4014 // Because this call is not awaited, execution of the current method continues before the call is completed
                    }
                    catch (Exception ex)
                    {
                        _logger.Error(ex, $"[{nameof(StreamMessageService)}].[{nameof(SendAsync)}] => Sending Stream Message");
                    }
                    locker.Release();
                }
            });
            IsStreaming = false;
            return true;
        }

        public bool CanSend(StreamMessageRequest? request)
        {
            return base.CanSend(request);
        }

        public async Task<bool> SendAsync(StreamMessageRequest request)
        {
            return await _sendChatMessageManager.SendAsync(request);
        }

        public async void Receive(StreamMessageResponse? response)
        {
            if (response is null || response.Stream is null || response.Stream.Length == 0)
                return;

            OnNewStreamMessage?.Invoke(response);
            await _receiveChatMessageManager.ReceiveAsync(response);
        }

        public Task StopStreaming()
        {
            if (IsStreaming)
                IsStreaming = false;
            return Task.CompletedTask;
        }
    }
}
