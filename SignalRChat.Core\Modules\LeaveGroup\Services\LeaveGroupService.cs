﻿namespace SignalRChat.Core.Modules.LeaveGroup.Services
{
    public class LeaveGroupService : ILeaveGroupService
    {
        private readonly ISignalRConnectionManager _signalRConnectionManager;
        private readonly ISession _session;
        private readonly ILogger _logger;

        public event LeaveGroupReceivedEventHandler OnGroupLeaved;

        public LeaveGroupService(ISignalRConnectionManager signalRConnectionManager, ISession session)
        {
            _signalRConnectionManager = signalRConnectionManager;
            _session = session;
            _logger = session.Logger;

            _signalRConnectionManager.OnLeaveGroupReceived += Receive;
        }

        public async Task<bool> SendAsync(Ulid groupId)
        {
            if (!groupId.IsValid())
                return false;

            try
            {
                _logger.Information($"[{nameof(LeaveGroupService)}].[{nameof(LeaveGroup)}] => Start Leaving Group [{groupId}]");
                LeaveGroupRequest request = new LeaveGroupRequest()
                {
                    GroupId = groupId,
                };
                bool result = await SendAsync(request);
                _logger.Information($"[{nameof(LeaveGroupService)}].[{nameof(LeaveGroup)}] => End Leaving Group [{groupId}]");
                return result;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"[{nameof(LeaveGroupService)}].[{nameof(LeaveGroup)}] => Leaving Group");
                return false;
            }
        }

        public bool CanSend(LeaveGroupRequest? request)
        {
            return
                request != null
                && request.GroupId.IsValid()
                && _session.IsOnLine;
        }

        public async Task<bool> SendAsync(LeaveGroupRequest request)
        {
            try
            {
                _logger.Information($"[{nameof(LeaveGroupService)}].[{nameof(SendAsync)}] => {nameof(request.GroupId)}: {request.GroupId}");
                await _signalRConnectionManager.InvokeCoreAsync("LeaveGroup", new object[] { request });
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        public void Receive(LeaveGroupResponse? response)
        {
            if (response == null || response.Group == null || response.User == null)
                return;

            _logger.Information($"[{nameof(LeaveGroupService)}].[{nameof(LeaveGroup)}] => Start Leaving [{response.User.Name}] From Group [{response.Group.Name}]");
            OnGroupLeaved?.Invoke(response);

            ChatCard? groupCard = _session.GetChatCard(response.Group.ClientId);
            if (_session.IsLoggedIn && groupCard != null)
            {
                ChatCard? userCard = groupCard.GroupChatCard.FirstOrDefault(x => x.ClientId == response.User.ClientId);
                if (userCard != null)
                {
                    groupCard.GroupChatCard.TryRemove(userCard);
                }
            }
            _logger.Information($"[{nameof(LeaveGroupService)}].[{nameof(LeaveGroup)}] => End Leaving [{response.User.Name}] From Group [{response.Group.Name}]");
        }
    }
}
