﻿namespace SignalRChat.Core.Data
{
    public class ApplicationDbContext : DbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
        {
        }

        public DbSet<Client> Clients { get; set; }
        public DbSet<ChatMessage> ChatMessages { get; set; }

        protected override void ConfigureConventions(ModelConfigurationBuilder configurationBuilder)
        {
            configurationBuilder.Properties<decimal>().HavePrecision(18, 5);
            configurationBuilder.Properties<Ulid>().HaveConversion<UlidToBytesConverter>();

            base.ConfigureConventions(configurationBuilder);
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            // Configure the primary key for ChatMessage
            modelBuilder.Entity<ChatMessage>().HasKey(cm => cm.MessageId);

            // Configure the relationship between ChatMessage and Sender (Client)
            modelBuilder.Entity<ChatMessage>()
                .HasOne(cm => cm.Sender)                                            // Navigation property in ChatMessage
                .WithMany(client => client.SentMessages)                            // Navigation property in Client
                .HasForeignKey(cm => cm.SenderId)                                   // Foreign key in ChatMessage
                .OnDelete(DeleteBehavior.Restrict);                                 // Avoid cascading deletes; set foreign key to null

            // Configure the relationship between ChatMessage and Receiver (Client)
            modelBuilder.Entity<ChatMessage>()
                .HasOne(cm => cm.Receiver)                                          // Navigation property in ChatMessage
                .WithMany(client => client.ReceivedMessages)                        // Navigation property in Client
                .HasForeignKey(cm => cm.ReceiverId)                                 // Foreign key in ChatMessage
                .OnDelete(DeleteBehavior.Restrict);                                 // Avoid cascading deletes; set foreign key to null

            // Configure inheritance for ChatMessage with a discriminator column
            modelBuilder.Entity<ChatMessage>()
                .HasDiscriminator<string>("Discriminator")                          // Specify the discriminator column
                .HasValue<ChatMessage>(nameof(ChatMessage))                         // Discriminator value for the base type
                .HasValue<TextChatMessage>(nameof(TextChatMessage))                 // Derived type: TextChatMessage
                .HasValue<ImageChatMessage>(nameof(ImageChatMessage))               // Derived type: ImageChatMessage
                .HasValue<RecordChatMessage>(nameof(RecordChatMessage));            // Derived type: RecordChatMessage

            // Configure the primary key for Client                               
            modelBuilder.Entity<Client>().HasKey(c => c.ClientId);

            // Configure inheritance for Client with a discriminator column
            modelBuilder.Entity<Client>()
                .HasDiscriminator<string>("Discriminator")                          // Specify the discriminator column
                .HasValue<Client>(nameof(Client))                                   // Discriminator value for the base type
                .HasValue<User>(nameof(User))                                       // Derived type: User
                .HasValue<Group>(nameof(Group));                                    // Derived type: Group

            // Configure the conversion for the ChatType property in Client
            modelBuilder.Entity<Client>()
                .Property(client => client.ChatType)                                // ChatType property in Client
                .HasConversion(
                    value => value.ToString(),                                      // Convert enum to string for storage
                    value => (ChatType)Enum.Parse(typeof(ChatType), value)          // Convert string back to enum
                );
        }
    }
}
