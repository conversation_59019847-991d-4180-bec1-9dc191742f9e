﻿namespace SignalRChat.Core.Modules.CreateGroup.Services
{
    public class CreateGroupService : ICreateGroupService
    {
        private readonly ISignalRConnectionManager _signalRConnectionManager;
        private readonly ISession _session;
        private readonly ILogger _logger;

        public event CreateGroupReceivedEventHandler OnGroupCreated;

        public CreateGroupService(ISignalRConnectionManager signalRConnectionManager, ISession session)
        {
            _signalRConnectionManager = signalRConnectionManager;
            _session = session;
            _logger = session.Logger;

            _signalRConnectionManager.OnCreateGroupReceived += Receive;
        }

        public async Task<bool> SendAsync(string groupName, byte[] photo)
        {
            if (string.IsNullOrEmpty(groupName))
                return false;

            try
            {
                _logger.Information($"[{nameof(CreateGroupService)}].[{nameof(CreateGroup)}] => Start Creating Group [{groupName}]");
                CreateGroupRequest request = new CreateGroupRequest()
                {
                    GroupId = UlidGenerator.Generator(groupName),
                    GroupName = groupName,
                    Photo = photo,
                };
                bool result = await SendAsync(request);
                _logger.Information($"[{nameof(CreateGroupService)}].[{nameof(CreateGroup)}] => End Creating Group [{groupName}]");
                return result;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"[{nameof(CreateGroupService)}].[{nameof(CreateGroup)}] => Creating Group");
                return false;
            }
        }

        public bool CanSend(CreateGroupRequest? request)
        {
            return
                _session.IsOnLine;
        }

        public async Task<bool> SendAsync(CreateGroupRequest request)
        {
            try
            {
                _logger.Information($"[{nameof(CreateGroupService)}].[{nameof(SendAsync)}] => {nameof(request.GroupName)}: {request.GroupName}{(request.Photo is null ? string.Empty : $", {nameof(request.Photo)}: {request.Photo.Length}")}");
                await _signalRConnectionManager.InvokeCoreAsync("CreateGroup", new object[] { request });
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        public void Receive(CreateGroupResponse? response)
        {
            if (response is null)
                return;

            _logger.Information($"[{nameof(CreateGroupService)}].[{nameof(CreateGroup)}] => Start Creating Group [{response.Group.Name}]");
            OnGroupCreated?.Invoke(response);

            ChatCard? ptp = _session.GetChatCard(response.Group.ClientId);
            if (_session.IsLoggedIn && ptp == null)
            {
                ChatCard chatCard = new ChatCard(response.Group);
                foreach (var user in response.Group.Users)
                {
                    ChatCard? parts = _session.GetChatCard(user.ClientId);
                    if (parts != null)
                        chatCard.GroupChatCard.Add(parts);
                }
                _session.ChatCards.TryAdd(chatCard);
            }
            _logger.Information($"[{nameof(CreateGroupService)}].[{nameof(CreateGroup)}] => End Creating Group [{response.Group.Name}]");
        }
    }
}
