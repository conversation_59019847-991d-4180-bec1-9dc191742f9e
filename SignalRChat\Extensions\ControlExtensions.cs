﻿namespace SignalRChat.Extensions
{
    public static class ControlExtensions
    {
        public static void SafelyInvokeAction(this Control control, Action action)
        {
            if (control == null || action == null || control.IsDisposed)
                return;

            try
            {
                if (control.InvokeRequired)
                    control.BeginInvoke(action);
                else
                    action();
            }
            catch (Exception ex)
            {
                // Log the exception (you can replace this with your logging mechanism)
                Console.WriteLine($"Exception in SafelyInvokeAction: {ex.Message}");
            }
        }
    }
}
