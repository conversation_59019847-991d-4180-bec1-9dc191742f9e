﻿namespace SignalRChat.CustomControls
{
    partial class Chat
    {
        /// <summary> 
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        #region Component Designer generated code

        /// <summary> 
        /// Required method for Designer support - do not modify 
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            components = new Container();
            tlpMain = new TableLayoutPanel();
            lblChatName = new Label();
            btnOptions = new MenuButton();
            individualChatMenuStrip = new ContextMenuStrip(components);
            groupChatMenuStrip = new ContextMenuStrip(components);
            tsmiLeaveGroup = new ToolStripMenuItem();
            tableLayoutPanel2 = new TableLayoutPanel();
            btnSendStream = new Button();
            btnSendRecord = new Button();
            btnSendBuzz = new Button();
            btnSendMessage = new Button();
            btnSendImage = new Button();
            txtMessage = new TextBox();
            tlpMain.SuspendLayout();
            groupChatMenuStrip.SuspendLayout();
            tableLayoutPanel2.SuspendLayout();
            SuspendLayout();
            // 
            // tlpMain
            // 
            tlpMain.ColumnCount = 3;
            tlpMain.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 200F));
            tlpMain.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            tlpMain.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 50F));
            tlpMain.Controls.Add(tableLayoutPanel2, 0, 2);
            tlpMain.Controls.Add(lblChatName, 0, 0);
            tlpMain.Controls.Add(btnOptions, 2, 0);
            tlpMain.Dock = DockStyle.Fill;
            tlpMain.Location = new Point(0, 0);
            tlpMain.Name = "tlpMain";
            tlpMain.RowCount = 3;
            tlpMain.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));
            tlpMain.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            tlpMain.RowStyles.Add(new RowStyle(SizeType.Absolute, 40F));
            tlpMain.Size = new Size(686, 512);
            tlpMain.TabIndex = 0;
            // 
            // lblChatName
            // 
            lblChatName.AutoSize = true;
            lblChatName.Dock = DockStyle.Fill;
            lblChatName.Location = new Point(3, 0);
            lblChatName.Name = "lblChatName";
            lblChatName.Size = new Size(194, 30);
            lblChatName.TabIndex = 1;
            lblChatName.Text = "Chat Name";
            // 
            // btnOptions
            // 
            btnOptions.Dock = DockStyle.Fill;
            btnOptions.Location = new Point(639, 3);
            btnOptions.Name = "btnOptions";
            btnOptions.Size = new Size(44, 24);
            btnOptions.TabIndex = 2;
            btnOptions.Text = "  ...";
            btnOptions.TextAlign = ContentAlignment.MiddleLeft;
            btnOptions.UseVisualStyleBackColor = true;
            // 
            // individualChatMenuStrip
            // 
            individualChatMenuStrip.Name = "individualChatMenuStrip";
            individualChatMenuStrip.Size = new Size(61, 4);
            // 
            // groupChatMenuStrip
            // 
            groupChatMenuStrip.Items.AddRange(new ToolStripItem[] { tsmiLeaveGroup });
            groupChatMenuStrip.Name = "groupChatMenuStrip";
            groupChatMenuStrip.Size = new Size(141, 26);
            // 
            // tsmiLeaveGroup
            // 
            tsmiLeaveGroup.Name = "tsmiLeaveGroup";
            tsmiLeaveGroup.Size = new Size(140, 22);
            tsmiLeaveGroup.Text = "Leave Group";
            // 
            // tableLayoutPanel2
            // 
            tableLayoutPanel2.ColumnCount = 6;
            tlpMain.SetColumnSpan(tableLayoutPanel2, 3);
            tableLayoutPanel2.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            tableLayoutPanel2.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 40F));
            tableLayoutPanel2.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 40F));
            tableLayoutPanel2.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 40F));
            tableLayoutPanel2.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 40F));
            tableLayoutPanel2.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 40F));
            tableLayoutPanel2.Controls.Add(btnSendStream, 2, 0);
            tableLayoutPanel2.Controls.Add(btnSendRecord, 3, 0);
            tableLayoutPanel2.Controls.Add(btnSendBuzz, 1, 0);
            tableLayoutPanel2.Controls.Add(btnSendMessage, 5, 0);
            tableLayoutPanel2.Controls.Add(btnSendImage, 4, 0);
            tableLayoutPanel2.Controls.Add(txtMessage, 0, 0);
            tableLayoutPanel2.Dock = DockStyle.Fill;
            tableLayoutPanel2.Location = new Point(3, 475);
            tableLayoutPanel2.Name = "tableLayoutPanel2";
            tableLayoutPanel2.RowCount = 1;
            tableLayoutPanel2.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            tableLayoutPanel2.Size = new Size(680, 34);
            tableLayoutPanel2.TabIndex = 3;
            // 
            // btnSendStream
            // 
            btnSendStream.BackgroundImage = Properties.Resources.send_stream_icon;
            btnSendStream.BackgroundImageLayout = ImageLayout.Stretch;
            btnSendStream.Dock = DockStyle.Fill;
            btnSendStream.Location = new Point(523, 3);
            btnSendStream.Name = "btnSendStream";
            btnSendStream.Size = new Size(34, 28);
            btnSendStream.TabIndex = 4;
            btnSendStream.UseVisualStyleBackColor = true;
            // 
            // btnSendRecord
            // 
            btnSendRecord.BackgroundImage = Properties.Resources.send_record_icon;
            btnSendRecord.BackgroundImageLayout = ImageLayout.Stretch;
            btnSendRecord.Dock = DockStyle.Fill;
            btnSendRecord.Location = new Point(563, 3);
            btnSendRecord.Name = "btnSendRecord";
            btnSendRecord.Size = new Size(34, 28);
            btnSendRecord.TabIndex = 3;
            btnSendRecord.UseVisualStyleBackColor = true;
            // 
            // btnSendBuzz
            // 
            btnSendBuzz.BackgroundImage = Properties.Resources.send_buzz_icon;
            btnSendBuzz.BackgroundImageLayout = ImageLayout.Stretch;
            btnSendBuzz.Dock = DockStyle.Fill;
            btnSendBuzz.Location = new Point(483, 3);
            btnSendBuzz.Name = "btnSendBuzz";
            btnSendBuzz.Size = new Size(34, 28);
            btnSendBuzz.TabIndex = 2;
            btnSendBuzz.UseVisualStyleBackColor = true;
            // 
            // btnSendMessage
            // 
            btnSendMessage.BackgroundImage = Properties.Resources.send_message_icon;
            btnSendMessage.BackgroundImageLayout = ImageLayout.Stretch;
            btnSendMessage.Dock = DockStyle.Fill;
            btnSendMessage.Location = new Point(643, 3);
            btnSendMessage.Name = "btnSendMessage";
            btnSendMessage.Size = new Size(34, 28);
            btnSendMessage.TabIndex = 0;
            btnSendMessage.UseVisualStyleBackColor = true;
            // 
            // btnSendImage
            // 
            btnSendImage.BackgroundImage = Properties.Resources.send_image_icon;
            btnSendImage.BackgroundImageLayout = ImageLayout.Stretch;
            btnSendImage.Dock = DockStyle.Fill;
            btnSendImage.Location = new Point(603, 3);
            btnSendImage.Name = "btnSendImage";
            btnSendImage.Size = new Size(34, 28);
            btnSendImage.TabIndex = 0;
            btnSendImage.UseVisualStyleBackColor = true;
            // 
            // txtMessage
            // 
            txtMessage.Anchor = AnchorStyles.Left | AnchorStyles.Right;
            txtMessage.Location = new Point(3, 5);
            txtMessage.Name = "txtMessage";
            txtMessage.Size = new Size(474, 23);
            txtMessage.TabIndex = 1;
            // 
            // Chat
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            Controls.Add(tlpMain);
            Name = "Chat";
            Size = new Size(686, 512);
            tlpMain.ResumeLayout(false);
            tlpMain.PerformLayout();
            groupChatMenuStrip.ResumeLayout(false);
            tableLayoutPanel2.ResumeLayout(false);
            tableLayoutPanel2.PerformLayout();
            ResumeLayout(false);
        }

        #endregion

        private TableLayoutPanel tlpMain;
        private Label lblChatName;
        private MenuButton btnOptions;
        private ContextMenuStrip individualChatMenuStrip;
        private ContextMenuStrip groupChatMenuStrip;
        private ToolStripMenuItem tsmiLeaveGroup;
        private TableLayoutPanel tableLayoutPanel2;
        private Button btnSendStream;
        private Button btnSendRecord;
        private Button btnSendBuzz;
        private Button btnSendMessage;
        private Button btnSendImage;
        private TextBox txtMessage;
    }
}
