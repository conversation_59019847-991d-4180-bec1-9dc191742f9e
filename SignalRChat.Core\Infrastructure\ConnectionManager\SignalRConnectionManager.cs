﻿namespace SignalRChat.Core.Infrastructure.ConnectionManager
{
    public class SignalRConnectionManagerOptions
    {
        public IDictionary<string, string> Headers { get; set; } = new Dictionary<string, string>();
        public string Url { get; set; }
        public Ulid UserId { get; set; }
        public string UserName { get; set; }
        public byte[] Photo { get; set; } = Array.Empty<byte>();
    }

    public class SignalRRetryPolicy : IRetryPolicy
    {
        public TimeSpan? NextRetryDelay(RetryContext retryContext)
        {
            return TimeSpan.FromSeconds(5);
        }
    }

    public class SignalRConnectionManager : ISignalRConnectionManager
    {
        public event Action OnConnectionReconnecting;
        public event Action OnConnectionReconnected;
        public event Action OnConnectionClosed;
        public event Action<UserDto> OnParticipantLoggedIn;
        public event Action<Ulid> OnParticipantLoggedOut;
        public event Action<Ulid> OnParticipantDisconnected;
        public event Action<Ulid> OnParticipantReconnected;

        public event LoginReceivedEventHandler OnLoginReceived;
        public event LogoutReceivedEventHandler OnLogoutReceived;

        public event ParticipantTypingReceivedEventHandler OnParticipantTypingReceived;
        public event TextMessageReceivedEventHandler OnTextMessageReceived;
        public event ImageMessageReceivedEventHandler OnImageMessageReceived;
        public event RecordMessageReceivedEventHandler OnRecordMessageReceived;
        public event StreamMessageReceivedEventHandler OnStreamMessageReceived;
        public event BuzzMessageReceivedEventHandler OnBuzzMessageReceived;
        public event CreateGroupReceivedEventHandler OnCreateGroupReceived;
        public event JoinGroupReceivedEventHandler OnJoinGroupReceived;
        public event LeaveGroupReceivedEventHandler OnLeaveGroupReceived;

        protected HubConnection Connection { get; private set; }
        public bool IsConnected => Connection?.State == HubConnectionState.Connected;

        private readonly ISession _session;
        private readonly ILogger _logger;

        public SignalRConnectionManager(ISession session)
        {
            _session = session;
            _logger = session.Logger;
        }

        public async Task Subscribe(SignalRConnectionManagerOptions signalRConnectionManagerOptions)
        {
            await InitializeConnectionAsync(signalRConnectionManagerOptions);
            if (IsConnected)
            {
                _session.IsConnected = true;
                InitialEvents();
            }
            else
                _session.IsConnected = false;
        }

        protected virtual async Task InitializeConnectionAsync(SignalRConnectionManagerOptions signalRConnectionManagerOptions)
        {
            Connection = new HubConnectionBuilder()
                .WithUrl(signalRConnectionManagerOptions.Url, options =>
                {
                    if (signalRConnectionManagerOptions.Headers.Count > 0)
                        foreach (var header in signalRConnectionManagerOptions.Headers)
                            options.Headers.TryAdd(header.Key, header.Value);
                })
                .WithAutomaticReconnect(new SignalRRetryPolicy())
                .AddJsonProtocol(options =>
                {
                    options.PayloadSerializerOptions.PropertyNamingPolicy = null;
                    options.PayloadSerializerOptions.IgnoreReadOnlyFields = true;
                    options.PayloadSerializerOptions.IgnoreReadOnlyProperties = true;
                    options.PayloadSerializerOptions.ReferenceHandler = ReferenceHandler.IgnoreCycles;
                })
                .Build();

            await Connection.StartAsync();

            _logger.Information($"[{nameof(SignalRConnectionManager)}] => Connected To Server");
        }

        protected virtual void InitialEvents()
        {
            Connection.On<UserDto>(nameof(Hubs.ISignalRChatHub.ParticipantLogin), ParticipantLogin);
            Connection.On<Ulid>(nameof(Hubs.ISignalRChatHub.ParticipantLogout), ParticipantLogoutOrDisconnection);
            Connection.On<Ulid>(nameof(Hubs.ISignalRChatHub.ParticipantDisconnection), ParticipantLogoutOrDisconnection);
            Connection.On<Ulid>(nameof(Hubs.ISignalRChatHub.ParticipantReconnection), ParticipantReconnection);

            Connection.On<LoginResponse?>(nameof(Hubs.ISignalRChatHub.Login), LoginReceived);
            Connection.On<LogoutResponse?>(nameof(Hubs.ISignalRChatHub.Logout), LogoutReceived);

            Connection.On<ParticipantTypingResponse?>(nameof(Hubs.ISignalRChatHub.ParticipantTyping), ParticipantTypingReceived);
            Connection.On<TextMessageResponse?>(nameof(Hubs.ISignalRChatHub.TextMessage), TextMessageReceived);
            Connection.On<ImageMessageResponse?>(nameof(Hubs.ISignalRChatHub.ImageMessage), ImageMessageReceived);
            Connection.On<RecordMessageResponse?>(nameof(Hubs.ISignalRChatHub.RecordMessage), RecordMessageReceived);
            Connection.On<StreamMessageResponse?>(nameof(Hubs.ISignalRChatHub.StreamMessage), StreamMessageReceived);
            Connection.On<BuzzMessageResponse?>(nameof(Hubs.ISignalRChatHub.BuzzMessage), BuzzMessageReceived);
            Connection.On<CreateGroupResponse?>(nameof(Hubs.ISignalRChatHub.CreateGroup), CreateGroupReceived);
            Connection.On<JoinGroupResponse?>(nameof(Hubs.ISignalRChatHub.JoinGroup), JoinGroupReceived);
            Connection.On<LeaveGroupResponse?>(nameof(Hubs.ISignalRChatHub.LeaveGroup), LeaveGroupReceived);

            Connection.Reconnecting += Reconnecting;
            Connection.Reconnected += Reconnected;
            Connection.Closed += Disconnected;

            ServicePointManager.DefaultConnectionLimit = 10;
        }

        #region SignalR Functions
        public async Task InvokeCoreAsync(string method, params object[] args)
        {
            await Connection.InvokeCoreAsync(method, args);
        }

        public async Task<T> InvokeCoreAsync<T>(string method, params object[] args)
        {
            return await Connection.InvokeCoreAsync<T>(method, args);
        }

        private async Task Reconnecting(Exception? exception)
        {
            _logger.Error(exception, $"[{nameof(SignalRConnectionManager)}].[{nameof(Reconnecting)}] => Reconnecting To Server");
            OnConnectionReconnecting?.Invoke();
            _session.IsConnected = true;

            if (_session.User != null)
            {
                if (!string.IsNullOrEmpty(_session.User.Name))
                {
                    LoginRequest request = new LoginRequest()
                    {
                        Id = _session.User.ClientId,
                        Name = _session.User.Name,
                        Photo = _session.User.Photo,
                    };
                    var result = await LoginAsync(request);
                    if (result != null)
                        _session.User = result.User;
                }
            }
        }

        private async Task Reconnected(string? arg)
        {
            _logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(Reconnected)}] => Reconnected To Server{(string.IsNullOrEmpty(arg) ? string.Empty : $" With args => [{arg}]")}");
            OnConnectionReconnected?.Invoke();
            _session.IsConnected = true;

            if (_session.User != null)
            {
                if (!string.IsNullOrEmpty(_session.User.Name))
                {
                    LoginRequest request = new LoginRequest()
                    {
                        Id = _session.User.ClientId,
                        Name = _session.User.Name,
                        Photo = _session.User.Photo,
                    };
                    var result = await LoginAsync(request);
                    if (result != null)
                        _session.User = result.User;
                }
            }
        }

        private async Task Disconnected(Exception? exception)
        {
            _logger.Error(exception, $"[{nameof(SignalRConnectionManager)}].[{nameof(Disconnected)}] => Disconnected From Server");
            OnConnectionClosed?.Invoke();

            _session.IsConnected = false;
            _session.IsLoggedIn = false;

            await Task.CompletedTask;
        }

        public async Task<LoginResponse?> LoginAsync(LoginRequest request)
        {
            _logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(LoginAsync)}] => {nameof(request.Name)}: {request.Name}");
            var result = await Connection.InvokeCoreAsync<LoginResponse?>("Login", new object[] { request });
            if (result != null)
                _session.IsLoggedIn = true;
            else
                _session.IsLoggedIn = false;
            return result;
        }

        public async Task LogoutAsync(LogoutRequest request)
        {
            _logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(LogoutAsync)}]");
            await Connection.InvokeCoreAsync("Logout", new object[] { request });
        }
        #endregion

        #region Event Handlers
        private void ParticipantLogin(UserDto user)
        {
            _logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(ParticipantLogin)}] => {nameof(user)}: {user.Name}");
            OnParticipantLoggedIn?.Invoke(user);

            var ptp = _session.GetChatCard(user.ClientId);
            if (_session.IsLoggedIn && ptp == null)
            {
                ChatCard chatCard = new ChatCard(user);
                _session.ChatCards.TryAdd(chatCard);
            }
        }

        private void ParticipantLogoutOrDisconnection(Ulid clientId)
        {
            _logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(ParticipantLogoutOrDisconnection)}] => Start Receiving Participant Disconnection Notification {nameof(clientId)}: {clientId}");
            OnParticipantLoggedOut?.Invoke(clientId);
            OnParticipantDisconnected?.Invoke(clientId);

            var person = _session.GetChatCard(clientId);
            if (person != null) person.IsLoggedIn = false;
            _logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(ParticipantLogoutOrDisconnection)}] => End Receiving Participant Disconnection Notification {nameof(clientId)}: {clientId}");
        }

        private void ParticipantReconnection(Ulid clientId)
        {
            _logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(ParticipantReconnection)}] => Start Receiving Participant Reconnection Notification {nameof(clientId)}: {clientId}");
            OnParticipantReconnected?.Invoke(clientId);

            var person = _session.GetChatCard(clientId);
            if (person != null) person.IsLoggedIn = true;
            _logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(ParticipantReconnection)}] => End Receiving Participant Reconnection Notification {nameof(clientId)}: {clientId}");
        }

        private void LoginReceived(LoginResponse? response)
        {
            if (response is null)
                return;
            _logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(LoginReceived)}] => Start Login As [{response.User.Name}]");
            OnLoginReceived?.Invoke(response);
            _logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(LoginReceived)}] => End Login As [{response.User.Name}]");
        }

        private void LogoutReceived(LogoutResponse? response)
        {
            if (response is null)
                return;
            _logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(LogoutReceived)}] => Start Logout As [{response.Id}]");
            OnLogoutReceived?.Invoke(response);
            _logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(LogoutReceived)}] => End Logout As [{response.Id}]");
        }

        private void ParticipantTypingReceived(ParticipantTypingResponse? response)
        {
            if (response is null)
                return;
            _logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(ParticipantTypingReceived)}] => Start Receiving Text Message From [{response?.SenderId}]");
            OnParticipantTypingReceived?.Invoke(response);
            _logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(ParticipantTypingReceived)}] => End Receiving Text Message From [{response?.SenderId}]");
        }

        private void TextMessageReceived(TextMessageResponse? response)
        {
            if (response is null)
                return;
            _logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(TextMessageReceived)}] => Start Receiving Text Message From [{response?.SenderId}]");
            OnTextMessageReceived?.Invoke(response);
            _logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(TextMessageReceived)}] => End Receiving Text Message From [{response?.SenderId}]");
        }

        private void ImageMessageReceived(ImageMessageResponse? response)
        {
            if (response is null)
                return;
            _logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(ImageMessageReceived)}] => Start Receiving Image Message From [{response?.SenderId}]");
            OnImageMessageReceived?.Invoke(response);
            _logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(ImageMessageReceived)}] => End Receiving Image Message From [{response?.SenderId}]");
        }

        private void RecordMessageReceived(RecordMessageResponse? response)
        {
            if (response is null)
                return;
            _logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(RecordMessageReceived)}] => Start Receiving Record Message From [{response?.SenderId}]");
            OnRecordMessageReceived?.Invoke(response);
            _logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(RecordMessageReceived)}] => End Receiving Record Message From [{response?.SenderId}]");
        }

        private void StreamMessageReceived(StreamMessageResponse? response)
        {
            if (response is null)
                return;
            _logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(StreamMessageReceived)}] => Start Receiving Stream Message From [{response?.SenderId}]");
            OnStreamMessageReceived?.Invoke(response);
            _logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(StreamMessageReceived)}] => End Receiving Stream Message From [{response?.SenderId}]");
        }

        private void BuzzMessageReceived(BuzzMessageResponse? response)
        {
            if (response is null)
                return;
            _logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(OnBuzzMessageReceived)}] => Start Receiving Buzz Message From [{response.SenderId}]");
            OnBuzzMessageReceived?.Invoke(response);
            _logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(OnBuzzMessageReceived)}] => End Receiving Buzz Message From [{response.SenderId}]");
        }

        private void CreateGroupReceived(CreateGroupResponse? response)
        {
            if (response is null)
                return;
            _logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(OnCreateGroupReceived)}] => Start Creating Group [{response.Group.Name}]");
            OnCreateGroupReceived?.Invoke(response);
            _logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(OnCreateGroupReceived)}] => End Creating Group [{response.Group.Name}]");
        }

        private void JoinGroupReceived(JoinGroupResponse? response)
        {
            if (response == null || response.Group == null || response.User == null)
                return;
            _logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(OnJoinGroupReceived)}] => Start Joining [{response.User.Name}] To Group [{response.Group.Name}]");
            OnJoinGroupReceived?.Invoke(response);
            _logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(OnJoinGroupReceived)}] => End Joining [{response.User.Name}] To Group [{response.Group.Name}]");
        }

        private void LeaveGroupReceived(LeaveGroupResponse? response)
        {
            if (response == null || response.Group == null || response.User == null)
                return;
            _logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(OnLeaveGroupReceived)}] => Start Leaving [{response.User.Name}] From Group [{response.Group.Name}]");
            OnLeaveGroupReceived?.Invoke(response);
            _logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(OnLeaveGroupReceived)}] => End Leaving [{response.User.Name}] From Group [{response.Group.Name}]");
        }
        #endregion
    }
}
