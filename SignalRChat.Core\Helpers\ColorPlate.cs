﻿namespace SignalRChat.Core.Helpers
{
    public static class ColorPlate
    {
        private static Color Primary50 = ColorTranslator.FromHtml("#e3f2fd");
        private static Color Primary50Foreground = ColorTranslator.FromHtml("#DD000000");
        private static Color Primary100 = ColorTranslator.FromHtml("#bbdefb");
        private static Color Primary100Foreground = ColorTranslator.FromHtml("#DD000000");
        private static Color Primary200 = ColorTranslator.FromHtml("#90caf9");
        private static Color Primary200Foreground = ColorTranslator.FromHtml("#DD000000");
        private static Color Primary300 = ColorTranslator.FromHtml("#64b5f6");
        private static Color Primary300Foreground = ColorTranslator.FromHtml("#DD000000");
        private static Color Primary400 = ColorTranslator.FromHtml("#42a5f5");
        private static Color Primary400Foreground = ColorTranslator.FromHtml("#DD000000");
        private static Color Primary500 = ColorTranslator.FromHtml("#2196f3");
        private static Color Primary500Foreground = ColorTranslator.FromHtml("#FFFFFFFF");
        private static Color Primary600 = ColorTranslator.FromHtml("#1e88e5");
        private static Color Primary600Foreground = ColorTranslator.FromHtml("#DDFFFFFF");
        private static Color Primary700 = ColorTranslator.FromHtml("#1976d2");
        private static Color Primary700Foreground = ColorTranslator.FromHtml("#DDFFFFFF");
        private static Color Primary800 = ColorTranslator.FromHtml("#1565c0");
        private static Color Primary800Foreground = ColorTranslator.FromHtml("#DDFFFFFF");
        private static Color Primary900 = ColorTranslator.FromHtml("#0d47a1");
        private static Color Primary900Foreground = ColorTranslator.FromHtml("#DDFFFFFF");

        public static Color APrimary50 = Primary50;
        public static Color APrimary100 = Primary100;
        public static Color HighlightBrush = Primary700;
        public static Color AccentColorBrush = Primary500;
        public static Color AccentColorBrush2 = Primary400;
        public static Color AccentColorBrush3 = Primary300;
        public static Color AccentColorBrush4 = Primary200;
        public static Color WindowTitleColorBrush = Primary700;
        public static Color AccentSelectedColorBrush = Primary500Foreground;
        public static Color CheckmarkFill = Primary500;
        public static Color RightArrowFill = Primary500;
        public static Color IdealForegroundColorBrush = Primary500Foreground;
        public static Color IdealForegroundDisabledBrush = Primary500;

        //<LinearGradientBrush x:Key="ProgressBrush" EndPoint="0.001,0.5" StartPoint="1.002,0.5">
        //    <GradientStop Color = "{DynamicResource Primary700}" Offset="0"/>
        //    <GradientStop Color = "{DynamicResource Primary300}" Offset="1"/>
        //</LinearGradientBrush>
    }

    public static class BaseColors
    {
        public static Color BackColor = Color.White;
    }

    public static class MessageColors
    {
        public static Color BackColor = Color.White;
        public static Color MessageBoard1 = ColorTranslator.FromHtml("#F2E3CD");
        public static Color MessageBoard2 = ColorTranslator.FromHtml("#2A82DA");
        public static Color MessageBackColor1 = ColorTranslator.FromHtml("#FFFAE6");
        public static Color MessageBackColor2 = ColorTranslator.FromHtml("#BBDEFB");
    }

    public static class ContactCardColors
    {
        public static Color BackColor = ColorTranslator.FromHtml("#E3F2FD");
        public static Color IsOnline1 = ColorTranslator.FromHtml("#F44336");
        public static Color IsOnline2 = ColorTranslator.FromHtml("#64DD17");
        public static Color IsSelected1 = ColorTranslator.FromHtml("#2196F3");
        public static Color IsSelected2 = ColorTranslator.FromHtml("#E3F2FD");
    }
}
