﻿namespace SignalRChat.DI
{
    public static class InjectSignalRChatLayerServices
    {
        public static IServiceCollection InjectSignalRChatServices<TSignalRConnectionManager>(this IServiceCollection services)
            where TSignalRConnectionManager : class, ISignalRConnectionManager
        {
            services.InjectSignalRChatCoreServices<TSignalRConnectionManager>();
            services.AddSingleton<LoginView>();
            services.AddTransient<ChatView>();

            return services;
        }

        public static IServiceCollection InjectSignalRChatServices(this IServiceCollection services)
        {
            services.InjectSignalRChatCoreServices();
            services.AddTransient<LoginView>();
            services.AddTransient<ChatView>();

            return services;
        }
    }
}
