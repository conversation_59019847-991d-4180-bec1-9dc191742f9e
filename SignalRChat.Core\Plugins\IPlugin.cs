using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace SignalRChat.Core.Plugins
{
    /// <summary>
    /// Base interface for all SignalR Chat plugins
    /// </summary>
    public interface IPlugin
    {
        /// <summary>
        /// Plugin unique identifier
        /// </summary>
        string Id { get; }

        /// <summary>
        /// Plugin name
        /// </summary>
        string Name { get; }

        /// <summary>
        /// Plugin version
        /// </summary>
        Version Version { get; }

        /// <summary>
        /// Plugin description
        /// </summary>
        string Description { get; }

        /// <summary>
        /// Plugin author
        /// </summary>
        string Author { get; }

        /// <summary>
        /// Plugin dependencies
        /// </summary>
        IEnumerable<PluginDependency> Dependencies { get; }

        /// <summary>
        /// Initialize the plugin
        /// </summary>
        Task InitializeAsync(IServiceProvider serviceProvider, Microsoft.Extensions.Logging.ILogger logger, CancellationToken cancellationToken = default);

        /// <summary>
        /// Configure services for the plugin
        /// </summary>
        void ConfigureServices(IServiceCollection services);

        /// <summary>
        /// Start the plugin
        /// </summary>
        Task StartAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Stop the plugin
        /// </summary>
        Task StopAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Dispose the plugin
        /// </summary>
        Task DisposeAsync();
    }

    /// <summary>
    /// Plugin dependency information
    /// </summary>
    public class PluginDependency
    {
        /// <summary>
        /// Dependency plugin ID
        /// </summary>
        public string PluginId { get; set; } = string.Empty;

        /// <summary>
        /// Minimum required version
        /// </summary>
        public Version? MinVersion { get; set; }

        /// <summary>
        /// Maximum supported version
        /// </summary>
        public Version? MaxVersion { get; set; }

        /// <summary>
        /// Whether the dependency is optional
        /// </summary>
        public bool IsOptional { get; set; } = false;
    }

    /// <summary>
    /// Interface for message processing plugins
    /// </summary>
    public interface IMessagePlugin : IPlugin
    {
        /// <summary>
        /// Process incoming message
        /// </summary>
        Task<MessageProcessingResult> ProcessIncomingMessageAsync(MessageContext context, CancellationToken cancellationToken = default);

        /// <summary>
        /// Process outgoing message
        /// </summary>
        Task<MessageProcessingResult> ProcessOutgoingMessageAsync(MessageContext context, CancellationToken cancellationToken = default);

        /// <summary>
        /// Supported message types
        /// </summary>
        IEnumerable<Type> SupportedMessageTypes { get; }
    }

    /// <summary>
    /// Interface for authentication plugins
    /// </summary>
    public interface IAuthenticationPlugin : IPlugin
    {
        /// <summary>
        /// Authenticate user
        /// </summary>
        Task<AuthenticationResult> AuthenticateAsync(AuthenticationContext context, CancellationToken cancellationToken = default);

        /// <summary>
        /// Validate token
        /// </summary>
        Task<ValidationResult> ValidateTokenAsync(string token, CancellationToken cancellationToken = default);

        /// <summary>
        /// Refresh token
        /// </summary>
        Task<TokenRefreshResult> RefreshTokenAsync(string refreshToken, CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// Interface for notification plugins
    /// </summary>
    public interface INotificationPlugin : IPlugin
    {
        /// <summary>
        /// Send notification
        /// </summary>
        Task<NotificationResult> SendNotificationAsync(NotificationContext context, CancellationToken cancellationToken = default);

        /// <summary>
        /// Supported notification types
        /// </summary>
        IEnumerable<NotificationType> SupportedTypes { get; }
    }

    /// <summary>
    /// Interface for storage plugins
    /// </summary>
    public interface IStoragePlugin : IPlugin
    {
        /// <summary>
        /// Store file
        /// </summary>
        Task<StorageResult> StoreFileAsync(StorageContext context, CancellationToken cancellationToken = default);

        /// <summary>
        /// Retrieve file
        /// </summary>
        Task<RetrievalResult> RetrieveFileAsync(string fileId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Delete file
        /// </summary>
        Task<DeletionResult> DeleteFileAsync(string fileId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Supported file types
        /// </summary>
        IEnumerable<string> SupportedFileTypes { get; }
    }

    /// <summary>
    /// Message processing context
    /// </summary>
    public class MessageContext
    {
        public object Message { get; set; } = null!;
        public string SenderId { get; set; } = string.Empty;
        public string ReceiverId { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public Dictionary<string, object> Properties { get; set; } = new();
    }

    /// <summary>
    /// Message processing result
    /// </summary>
    public class MessageProcessingResult
    {
        public bool Success { get; set; }
        public object? ProcessedMessage { get; set; }
        public string? ErrorMessage { get; set; }
        public bool ShouldContinueProcessing { get; set; } = true;
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// Authentication context
    /// </summary>
    public class AuthenticationContext
    {
        public string Username { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        public Dictionary<string, string> Claims { get; set; } = new();
        public string? ClientId { get; set; }
        public string? IpAddress { get; set; }
    }

    /// <summary>
    /// Authentication result
    /// </summary>
    public class AuthenticationResult
    {
        public bool Success { get; set; }
        public string? Token { get; set; }
        public string? RefreshToken { get; set; }
        public DateTime? ExpiresAt { get; set; }
        public string? ErrorMessage { get; set; }
        public Dictionary<string, object> Claims { get; set; } = new();
    }

    /// <summary>
    /// Token validation result
    /// </summary>
    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public string? UserId { get; set; }
        public Dictionary<string, object> Claims { get; set; } = new();
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// Token refresh result
    /// </summary>
    public class TokenRefreshResult
    {
        public bool Success { get; set; }
        public string? NewToken { get; set; }
        public string? NewRefreshToken { get; set; }
        public DateTime? ExpiresAt { get; set; }
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// Notification context
    /// </summary>
    public class NotificationContext
    {
        public string UserId { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public NotificationType Type { get; set; }
        public Dictionary<string, object> Data { get; set; } = new();
        public DateTime? ScheduledAt { get; set; }
    }

    /// <summary>
    /// Notification types
    /// </summary>
    public enum NotificationType
    {
        Push,
        Email,
        SMS,
        InApp,
        Webhook
    }

    /// <summary>
    /// Notification result
    /// </summary>
    public class NotificationResult
    {
        public bool Success { get; set; }
        public string? NotificationId { get; set; }
        public string? ErrorMessage { get; set; }
        public DateTime? DeliveredAt { get; set; }
    }

    /// <summary>
    /// Storage context
    /// </summary>
    public class StorageContext
    {
        public string FileName { get; set; } = string.Empty;
        public string ContentType { get; set; } = string.Empty;
        public Stream Content { get; set; } = null!;
        public long ContentLength { get; set; }
        public Dictionary<string, string> Metadata { get; set; } = new();
        public string? UserId { get; set; }
    }

    /// <summary>
    /// Storage result
    /// </summary>
    public class StorageResult
    {
        public bool Success { get; set; }
        public string? FileId { get; set; }
        public string? Url { get; set; }
        public string? ErrorMessage { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// File retrieval result
    /// </summary>
    public class RetrievalResult
    {
        public bool Success { get; set; }
        public Stream? Content { get; set; }
        public string? ContentType { get; set; }
        public long ContentLength { get; set; }
        public string? ErrorMessage { get; set; }
        public Dictionary<string, string> Metadata { get; set; } = new();
    }

    /// <summary>
    /// File deletion result
    /// </summary>
    public class DeletionResult
    {
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
    }
}
