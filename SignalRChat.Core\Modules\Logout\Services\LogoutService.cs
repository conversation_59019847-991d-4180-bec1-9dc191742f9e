﻿namespace SignalRChat.Core.Modules.Logout.Services
{
    public class LogoutService : ILogoutService
    {
        private readonly ISignalRConnectionManager _signalRConnectionManager;
        private readonly ISession _session;
        private readonly ILogger _logger;

        public event LogoutReceivedEventHandler OnNewLogout;

        public LogoutService(ISignalRConnectionManager signalRConnectionManager, ISession session)
        {
            _signalRConnectionManager = signalRConnectionManager;
            _session = session;
            _logger = session.Logger;

            _signalRConnectionManager.OnLogoutReceived += Receive;
        }

        public async Task<bool> SendAsync()
        {
            LogoutRequest request = new LogoutRequest()
            {
                Id = _session.User.ClientId,
            };
            if (CanSend(request))
                return await SendAsync(request);
            return false;
        }

        public bool CanSend(LogoutRequest? request)
        {
            return
                request != null
                && request.Id.IsValid()
                && _session.IsConnected;
        }

        public async Task<bool> SendAsync(LogoutRequest request)
        {
            try
            {
                _logger.Information($"[{nameof(LogoutService)}].[{nameof(Logout)}] => Start Logging Out");
                await _signalRConnectionManager.LogoutAsync(request);
                _logger.Information($"[{nameof(LogoutService)}].[{nameof(Logout)}] => End Logging Out");
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        public void Receive(LogoutResponse? response)
        {
            if (response is null)
                return;

            OnNewLogout?.Invoke(response);
        }
    }
}
