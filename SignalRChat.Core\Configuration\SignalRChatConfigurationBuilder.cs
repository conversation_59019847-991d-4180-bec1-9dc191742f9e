using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using System.ComponentModel.DataAnnotations;

namespace SignalRChat.Core.Configuration
{
    /// <summary>
    /// Configuration builder for SignalR Chat application
    /// </summary>
    public class SignalRChatConfigurationBuilder
    {
        private readonly IServiceCollection _services;
        private readonly IConfiguration _configuration;
        private readonly SignalRChatOptions _options;

        public SignalRChatConfigurationBuilder(IServiceCollection services, IConfiguration configuration)
        {
            _services = services ?? throw new ArgumentNullException(nameof(services));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _options = new SignalRChatOptions();
        }

        /// <summary>
        /// Configure database options
        /// </summary>
        public SignalRChatConfigurationBuilder ConfigureDatabase(Action<DatabaseOptions> configure)
        {
            configure?.Invoke(_options.Database);
            return this;
        }

        /// <summary>
        /// Configure SignalR hub options
        /// </summary>
        public SignalRChatConfigurationBuilder ConfigureHub(Action<SignalRHubOptions> configure)
        {
            configure?.Invoke(_options.Hub);
            return this;
        }

        /// <summary>
        /// Configure logging options
        /// </summary>
        public SignalRChatConfigurationBuilder ConfigureLogging(Action<LoggingOptions> configure)
        {
            configure?.Invoke(_options.Logging);
            return this;
        }

        /// <summary>
        /// Configure CORS options
        /// </summary>
        public SignalRChatConfigurationBuilder ConfigureCors(Action<CorsOptions> configure)
        {
            configure?.Invoke(_options.Cors);
            return this;
        }

        /// <summary>
        /// Configure security options
        /// </summary>
        public SignalRChatConfigurationBuilder ConfigureSecurity(Action<SecurityOptions> configure)
        {
            configure?.Invoke(_options.Security);
            return this;
        }

        /// <summary>
        /// Configure message options
        /// </summary>
        public SignalRChatConfigurationBuilder ConfigureMessages(Action<MessageOptions> configure)
        {
            configure?.Invoke(_options.Messages);
            return this;
        }

        /// <summary>
        /// Configure plugin options
        /// </summary>
        public SignalRChatConfigurationBuilder ConfigurePlugins(Action<PluginOptions> configure)
        {
            configure?.Invoke(_options.Plugins);
            return this;
        }

        /// <summary>
        /// Configure performance options
        /// </summary>
        public SignalRChatConfigurationBuilder ConfigurePerformance(Action<PerformanceOptions> configure)
        {
            configure?.Invoke(_options.Performance);
            return this;
        }

        /// <summary>
        /// Configure integration options
        /// </summary>
        public SignalRChatConfigurationBuilder ConfigureIntegration(Action<IntegrationOptions> configure)
        {
            configure?.Invoke(_options.Integration);
            return this;
        }

        /// <summary>
        /// Load configuration from appsettings.json
        /// </summary>
        public SignalRChatConfigurationBuilder LoadFromConfiguration()
        {
            var section = _configuration.GetSection(SignalRChatOptions.SectionName);
            if (section.Exists())
            {
                section.Bind(_options);
            }
            return this;
        }

        /// <summary>
        /// Load configuration from environment variables
        /// </summary>
        public SignalRChatConfigurationBuilder LoadFromEnvironment(string prefix = "SIGNALRCHAT_")
        {
            var envConfig = new ConfigurationBuilder()
                .AddEnvironmentVariables(prefix)
                .Build();

            envConfig.Bind(_options);
            return this;
        }

        /// <summary>
        /// Load configuration from command line arguments
        /// </summary>
        public SignalRChatConfigurationBuilder LoadFromCommandLine(string[] args)
        {
            var cmdConfig = new ConfigurationBuilder()
                .AddCommandLine(args)
                .Build();

            cmdConfig.Bind(_options);
            return this;
        }

        /// <summary>
        /// Validate configuration options
        /// </summary>
        public SignalRChatConfigurationBuilder ValidateConfiguration()
        {
            var validator = new SignalRChatOptionsValidator();
            var validationResult = validator.Validate(_options);

            if (validationResult.Failed && validationResult.Failures != null)
            {
                var errors = string.Join(Environment.NewLine, validationResult.Failures);
                throw new InvalidOperationException($"Configuration validation failed:{Environment.NewLine}{errors}");
            }

            return this;
        }

        /// <summary>
        /// Build and register configuration
        /// </summary>
        public SignalRChatOptions Build()
        {
            // Register options with DI container
            _services.Configure<SignalRChatOptions>(options =>
            {
                options.Database = _options.Database;
                options.Hub = _options.Hub;
                options.Logging = _options.Logging;
                options.Cors = _options.Cors;
                options.Security = _options.Security;
                options.Messages = _options.Messages;
                options.Plugins = _options.Plugins;
                options.Performance = _options.Performance;
                options.Integration = _options.Integration;
            });

            // Register individual option sections
            _services.Configure<DatabaseOptions>(options => CopyProperties(_options.Database, options));
            _services.Configure<SignalRHubOptions>(options => CopyProperties(_options.Hub, options));
            _services.Configure<LoggingOptions>(options => CopyProperties(_options.Logging, options));
            _services.Configure<CorsOptions>(options => CopyProperties(_options.Cors, options));
            _services.Configure<SecurityOptions>(options => CopyProperties(_options.Security, options));
            _services.Configure<MessageOptions>(options => CopyProperties(_options.Messages, options));
            _services.Configure<PluginOptions>(options => CopyProperties(_options.Plugins, options));
            _services.Configure<PerformanceOptions>(options => CopyProperties(_options.Performance, options));
            _services.Configure<IntegrationOptions>(options => CopyProperties(_options.Integration, options));

            // Enable options validation
            _services.AddSingleton<IValidateOptions<SignalRChatOptions>, SignalRChatOptionsValidator>();

            return _options;
        }

        private static void CopyProperties<T>(T source, T destination) where T : class
        {
            var properties = typeof(T).GetProperties();
            foreach (var property in properties)
            {
                if (property.CanRead && property.CanWrite)
                {
                    var value = property.GetValue(source);
                    property.SetValue(destination, value);
                }
            }
        }
    }

    /// <summary>
    /// Configuration validator for SignalR Chat options
    /// </summary>
    public class SignalRChatOptionsValidator : IValidateOptions<SignalRChatOptions>
    {
        public ValidateOptionsResult Validate(string? name, SignalRChatOptions options)
        {
            return Validate(options);
        }

        public ValidateOptionsResult Validate(SignalRChatOptions options)
        {
            var errors = new List<string>();

            // Validate using data annotations
            var validationContext = new ValidationContext(options);
            var validationResults = new List<System.ComponentModel.DataAnnotations.ValidationResult>();
            
            if (!Validator.TryValidateObject(options, validationContext, validationResults))
            {
                errors.AddRange(validationResults.Select(vr => vr.ErrorMessage ?? "Unknown validation error"));
            }

            // Custom validation logic
            ValidateDatabase(options.Database, errors);
            ValidateHub(options.Hub, errors);
            ValidateCors(options.Cors, errors);
            ValidateSecurity(options.Security, errors);
            ValidateMessages(options.Messages, errors);
            ValidatePerformance(options.Performance, errors);

            return errors.Count > 0 
                ? ValidateOptionsResult.Fail(errors)
                : ValidateOptionsResult.Success;
        }

        private static void ValidateDatabase(DatabaseOptions database, List<string> errors)
        {
            if (database.Provider != DatabaseProvider.InMemory && string.IsNullOrEmpty(database.ConnectionString))
            {
                errors.Add("Connection string is required when not using InMemory database provider");
            }

            if (database.ConnectionTimeoutSeconds <= 0)
            {
                errors.Add("Connection timeout must be greater than 0");
            }
        }

        private static void ValidateHub(SignalRHubOptions hub, List<string> errors)
        {
            if (string.IsNullOrEmpty(hub.HubPath))
            {
                errors.Add("Hub path cannot be empty");
            }

            if (!hub.HubPath.StartsWith('/'))
            {
                errors.Add("Hub path must start with '/'");
            }
        }

        private static void ValidateCors(CorsOptions cors, List<string> errors)
        {
            if (cors.AllowedOrigins.Count == 0)
            {
                errors.Add("At least one allowed origin must be specified");
            }

            foreach (var origin in cors.AllowedOrigins)
            {
                if (string.IsNullOrEmpty(origin))
                {
                    errors.Add("CORS allowed origins cannot contain empty values");
                    break;
                }
            }
        }

        private static void ValidateSecurity(SecurityOptions security, List<string> errors)
        {
            if (security.EnableAuthentication && security.Jwt != null)
            {
                if (string.IsNullOrEmpty(security.Jwt.SecretKey))
                {
                    errors.Add("JWT secret key is required when authentication is enabled");
                }

                if (string.IsNullOrEmpty(security.Jwt.Issuer))
                {
                    errors.Add("JWT issuer is required when authentication is enabled");
                }
            }
        }

        private static void ValidateMessages(MessageOptions messages, List<string> errors)
        {
            if (messages.Text.MaxLength <= 0)
            {
                errors.Add("Maximum text message length must be greater than 0");
            }

            if (messages.Image.MaxSizeMB <= 0)
            {
                errors.Add("Maximum image size must be greater than 0");
            }

            if (messages.Audio.MaxDurationSeconds <= 0)
            {
                errors.Add("Maximum audio duration must be greater than 0");
            }
        }

        private static void ValidatePerformance(PerformanceOptions performance, List<string> errors)
        {
            if (performance.ConnectionPool.MaxConcurrentConnections <= 0)
            {
                errors.Add("Maximum concurrent connections must be greater than 0");
            }

            if (performance.Threading.MaxWorkerThreads <= 0)
            {
                errors.Add("Maximum worker threads must be greater than 0");
            }
        }
    }

    /// <summary>
    /// Extension methods for recursive validation
    /// </summary>
    public static class ValidationExtensions
    {
        public static bool TryValidateObjectRecursively<T>(object obj, ValidationContext validationContext, ICollection<System.ComponentModel.DataAnnotations.ValidationResult> results)
        {
            return Validator.TryValidateObject(obj, validationContext, results, true);
        }
    }
}
