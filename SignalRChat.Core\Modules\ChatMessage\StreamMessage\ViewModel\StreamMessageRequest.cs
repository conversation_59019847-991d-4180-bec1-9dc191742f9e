﻿namespace SignalRChat.Core.Modules.ChatMessage.StreamMessage.ViewModel
{
    public record StreamMessageRequest : BaseChatMessageRequest
    {
        [JsonConstructor]
        public StreamMessageRequest(Ulid messageId, Ulid senderId, Ulid receiverId, Ulid streamId, byte[] stream) : this(messageId, senderId, receiverId, streamId)
        {
            Stream = stream;
        }

        public StreamMessageRequest(Ulid messageId, Ulid senderId, Ulid receiverId, Ulid streamId) : base(messageId, senderId, receiverId)
        {
            StreamId = streamId;
        }

        [JsonInclude]
        public Ulid StreamId { get; private set; }
        [JsonInclude]
        public byte[] Stream { get; set; }

        public override bool IsValid()
        {
            return
                base.IsValid()
                && StreamId.IsValid()
                && Stream != null
                && Stream.Length > 0;
        }
    }
}
