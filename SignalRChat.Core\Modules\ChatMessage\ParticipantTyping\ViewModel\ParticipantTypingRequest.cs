﻿namespace SignalRChat.Core.Modules.ChatMessage.ParticipantTyping.ViewModel
{
    public record ParticipantTypingRequest : BaseChatMessageRequest
    {
        [JsonConstructor]
        public ParticipantTypingRequest(Ulid messageId, Ulid senderId, Ulid receiverId) : base(messageId, senderId, receiverId)
        {
        }

        public override bool IsValid()
        {
            return
                base.IsValid();
        }
    }
}
